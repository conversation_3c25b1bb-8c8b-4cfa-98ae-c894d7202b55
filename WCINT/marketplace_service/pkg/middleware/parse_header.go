package middleware

import "github.com/gin-gonic/gin"

// 请求头参数
const (
	AppChannel    = "app_channel"
	AppVersion    = "app_version"
	ClientType    = "client_type"
	Ip            = "ip"
	UserAgent     = "user_agent"
	Authorization = "Authorization"
)

func ParseHead(ctx *gin.Context) {
	ctx.Set(AppVersion, ctx.GetHeader("app_version"))
	ctx.Set(AppChannel, ctx.GetHeader("app_channel"))
	ctx.Set(ClientType, ctx.GetHeader("client_type"))
	ctx.Set(Ip, ctx.ClientIP())
	ctx.Set(UserAgent, ctx.Request.UserAgent())
	ctx.Set(Authorization, ctx.GetHeader("Authorization"))
}
