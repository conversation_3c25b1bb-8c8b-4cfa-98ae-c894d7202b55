package search

import (
	"fmt"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"reflect"
	"time"
)

// QueryWrapper 查询包装器
type QueryWrapper struct {
	ScopeOpts    []ScopeOpt
	SelectFields []field.Expr
	OrderBy      []field.Expr
	OrConditions [][]field.Expr // 修改为二维数组，每个子数组表示一组 OR 条件
}

// QueryBuilder 查询构建器
type QueryBuilder struct {
	queryWrapper QueryWrapper
	currentOr    []field.Expr // 当前正在构建的 OR 条件组
}

// OrBuilder OR 条件构建器
type OrBuilder struct {
	parent     *QueryBuilder
	conditions []field.Expr // 简化为直接存储表达式
}

// NewQueryBuilder 创建新的查询构建器
func NewQueryBuilder() *QueryBuilder {
	return &QueryBuilder{
		queryWrapper: QueryWrapper{
			ScopeOpts:    make([]ScopeOpt, 0),
			SelectFields: make([]field.Expr, 0),
			OrderBy:      make([]field.Expr, 0),
			OrConditions: make([][]field.Expr, 0),
		},
		currentOr: nil,
	}
}

// Or 开始一个新的 OR 条件组
func (qb *QueryBuilder) Or() *OrBuilder {
	return &OrBuilder{
		parent:     qb,
		conditions: make([]field.Expr, 0),
	}
}

// Done 完成 OR 条件的构建
func (ob *OrBuilder) Done() *QueryBuilder {
	if len(ob.conditions) > 0 {
		ob.parent.queryWrapper.OrConditions = append(ob.parent.queryWrapper.OrConditions, ob.conditions)
	}
	return ob.parent
}

// addExpr 添加表达式到 OR 条件
func (ob *OrBuilder) addExpr(expr field.Expr) {
	ob.conditions = append(ob.conditions, expr)
}

// Build 构建最终的查询条件
func (qb *QueryBuilder) Build() *QueryWrapper {
	// 处理基本的 WHERE 条件（已经在之前的 Eq、Gt 等方法中添加）

	// 处理 OR 条件组
	if len(qb.queryWrapper.OrConditions) > 0 {
		for _, orGroup := range qb.queryWrapper.OrConditions {
			if len(orGroup) > 0 {
				qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, func(dao gen.Dao) gen.Dao {
					return dao.Where(field.Or(orGroup...))
				})
			}
		}
	}
	return &qb.queryWrapper
}

func newScopeOpt(expr field.Expr) ScopeOpt {
	return func(dao gen.Dao) gen.Dao {
		return dao.Where(expr)
	}
}

func (qb *QueryBuilder) Eq(fieldV any, value any) *QueryBuilder {
	switch fieldV.(type) {
	case field.Bool:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Bool).Is(value.(bool))))
	case field.Int:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int).Eq(value.(int))))
	case field.Int8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int8).Eq(value.(int8))))
	case field.Int16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int16).Eq(value.(int16))))
	case field.Int32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int32).Eq(value.(int32))))
	case field.Int64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int64).Eq(value.(int64))))
	case field.Uint:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint).Eq(value.(uint))))
	case field.Uint8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint8).Eq(value.(uint8))))
	case field.Uint16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint16).Eq(value.(uint16))))
	case field.Uint32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint32).Eq(value.(uint32))))
	case field.Uint64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint64).Eq(value.(uint64))))
	case field.Float32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float32).Eq(value.(float32))))
	case field.Float64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float64).Eq(value.(float64))))
	case field.Time:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Time).Eq(value.(time.Time))))
	case field.String:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.String).Eq(value.(string))))
	default:
		panic("field type not supported")
	}
	return qb
}

func (qb *QueryBuilder) Ne(fieldV any, value any) *QueryBuilder {
	switch fieldV.(type) {
	case field.Bool:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Bool).Not().Is(value.(bool))))
	case field.Int:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int).Neq(value.(int))))
	case field.Int8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int8).Neq(value.(int8))))
	case field.Int16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int16).Neq(value.(int16))))
	case field.Int32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int32).Neq(value.(int32))))
	case field.Int64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int64).Neq(value.(int64))))
	case field.Uint:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint).Neq(value.(uint))))
	case field.Uint8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint8).Neq(value.(uint8))))
	case field.Uint16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint16).Neq(value.(uint16))))
	case field.Uint32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint32).Neq(value.(uint32))))
	case field.Uint64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint64).Neq(value.(uint64))))
	case field.Float32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float32).Neq(value.(float32))))
	case field.Float64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float64).Neq(value.(float64))))
	case field.Time:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Time).Neq(value.(time.Time))))
	case field.String:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.String).Neq(value.(string))))
	default:
		panic("field type not supported")
	}
	return qb
}

func (qb *QueryBuilder) Gt(fieldV any, value any) *QueryBuilder {
	switch fieldV.(type) {
	case field.Int:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int).Gt(value.(int))))
	case field.Int8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int8).Gt(value.(int8))))
	case field.Int16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int16).Gt(value.(int16))))
	case field.Int32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int32).Gt(value.(int32))))
	case field.Int64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int64).Gt(value.(int64))))
	case field.Uint:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint).Gt(value.(uint))))
	case field.Uint8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint8).Gt(value.(uint8))))
	case field.Uint16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint16).Gt(value.(uint16))))
	case field.Uint32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint32).Gt(value.(uint32))))
	case field.Uint64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint64).Gt(value.(uint64))))
	case field.Float32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float32).Gt(value.(float32))))
	case field.Float64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float64).Gt(value.(float64))))
	case field.Time:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Time).Gt(value.(time.Time))))
	case field.String:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.String).Gt(value.(string))))
	default:
		panic("field type not supported")
	}
	return qb
}

func (qb *QueryBuilder) Gte(fieldV any, value any) *QueryBuilder {
	switch fieldV.(type) {
	case field.Int:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int).Gte(value.(int))))
	case field.Int8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int8).Gte(value.(int8))))
	case field.Int16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int16).Gte(value.(int16))))
	case field.Int32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int32).Gte(value.(int32))))
	case field.Int64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int64).Gte(value.(int64))))
	case field.Uint:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint).Gte(value.(uint))))
	case field.Uint8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint8).Gte(value.(uint8))))
	case field.Uint16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint16).Gte(value.(uint16))))
	case field.Uint32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint32).Gte(value.(uint32))))
	case field.Uint64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint64).Gte(value.(uint64))))
	case field.Float32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float32).Gte(value.(float32))))
	case field.Float64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float64).Gte(value.(float64))))
	case field.Time:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Time).Gte(value.(time.Time))))
	case field.String:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.String).Gte(value.(string))))
	default:
		panic("field type not supported")
	}
	return qb
}

func (qb *QueryBuilder) Lt(fieldV any, value any) *QueryBuilder {
	switch fieldV.(type) {
	case field.Int:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int).Lt(value.(int))))
	case field.Int8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int8).Lt(value.(int8))))
	case field.Int16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int16).Lt(value.(int16))))
	case field.Int32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int32).Lt(value.(int32))))
	case field.Int64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int64).Lt(value.(int64))))
	case field.Uint:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint).Lt(value.(uint))))
	case field.Uint8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint8).Lt(value.(uint8))))
	case field.Uint16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint16).Lt(value.(uint16))))
	case field.Uint32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint32).Lt(value.(uint32))))
	case field.Uint64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint64).Lt(value.(uint64))))
	case field.Float32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float32).Lt(value.(float32))))
	case field.Float64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float64).Lt(value.(float64))))
	case field.Time:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Time).Lt(value.(time.Time))))
	case field.String:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.String).Lt(value.(string))))
	default:
		panic("field type not supported")
	}
	return qb
}

func (qb *QueryBuilder) Lte(fieldV any, value any) *QueryBuilder {
	switch fieldV.(type) {
	case field.Int:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int).Lte(value.(int))))
	case field.Int8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int8).Lte(value.(int8))))
	case field.Int16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int16).Lte(value.(int16))))
	case field.Int32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int32).Lte(value.(int32))))
	case field.Int64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int64).Lte(value.(int64))))
	case field.Uint:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint).Lte(value.(uint))))
	case field.Uint8:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint8).Lte(value.(uint8))))
	case field.Uint16:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint16).Lte(value.(uint16))))
	case field.Uint32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint32).Lte(value.(uint32))))
	case field.Uint64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint64).Lte(value.(uint64))))
	case field.Float32:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float32).Lte(value.(float32))))
	case field.Float64:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float64).Lte(value.(float64))))
	case field.Time:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Time).Lte(value.(time.Time))))
	case field.String:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.String).Lte(value.(string))))
	default:
		panic("field type not supported")
	}
	return qb
}

func (qb *QueryBuilder) In(fieldV any, value any) *QueryBuilder {
	switch fieldV.(type) {
	case field.Int:
		slice, err := convertToSlice[int](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int).In(slice...)))
	case field.Int8:
		slice, err := convertToSlice[int8](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int8).In(slice...)))
	case field.Int16:
		slice, err := convertToSlice[int16](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int16).In(slice...)))
	case field.Int32:
		slice, err := convertToSlice[int32](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int32).In(slice...)))
	case field.Int64:
		slice, err := convertToSlice[int64](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Int64).In(slice...)))
	case field.Uint:
		slice, err := convertToSlice[uint](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint).In(slice...)))
	case field.Uint8:
		slice, err := convertToSlice[uint8](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint8).In(slice...)))
	case field.Uint16:
		slice, err := convertToSlice[uint16](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint16).In(slice...)))
	case field.Uint32:
		slice, err := convertToSlice[uint32](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint32).In(slice...)))
	case field.Uint64:
		slice, err := convertToSlice[uint64](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Uint64).In(slice...)))
	case field.Float32:
		slice, err := convertToSlice[float32](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float32).In(slice...)))
	case field.Float64:
		slice, err := convertToSlice[float64](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Float64).In(slice...)))
	case field.Time:
		slice, err := convertToSlice[time.Time](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.Time).In(slice...)))
	case field.String:
		slice, err := convertToSlice[string](value)
		if err != nil {
			panic(err)
		}
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.String).In(slice...)))
	default:
		panic("field type not supported")
	}
	return qb
}

func (qb *QueryBuilder) Like(fieldV any, value string) *QueryBuilder {
	switch fieldV.(type) {
	case field.String:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.String).Like(value)))
	default:
		panic("field type not supported")
	}
	return qb
}

func (qb *QueryBuilder) NotLike(fieldV any, value string) *QueryBuilder {
	switch fieldV.(type) {
	case field.String:
		qb.queryWrapper.ScopeOpts = append(qb.queryWrapper.ScopeOpts, newScopeOpt(fieldV.(field.String).NotLike(value)))
	default:
		panic("field type not supported")
	}
	return qb
}

func (qb *QueryBuilder) Select(fieldVs ...any) *QueryBuilder {
	for i := range fieldVs {
		fieldV := fieldVs[i]
		switch fieldV.(type) {
		case field.Bool:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Bool))
		case field.Int:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Int))
		case field.Int8:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Int8))
		case field.Int16:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Int16))
		case field.Int32:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Int32))
		case field.Int64:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Int64))
		case field.Uint:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Uint))
		case field.Uint8:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Uint8))
		case field.Uint16:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Uint16))
		case field.Uint32:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Uint32))
		case field.Uint64:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Uint64))
		case field.Float32:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Float32))
		case field.Float64:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Float64))
		case field.Time:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.Time))
		case field.String:
			qb.queryWrapper.SelectFields = append(qb.queryWrapper.SelectFields, fieldV.(field.String))
		default:
			panic("field type not supported")
		}
	}

	return qb
}

func (qb *QueryBuilder) OrderByAsc(fieldVs ...any) *QueryBuilder {
	for i := range fieldVs {
		fieldV := fieldVs[i]
		switch fieldV.(type) {
		case field.Int:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Int).Asc())
		case field.Int8:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Int8).Asc())
		case field.Int16:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Int16).Asc())
		case field.Int32:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Int32).Asc())
		case field.Int64:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Int64).Asc())
		case field.Uint:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Uint).Asc())
		case field.Uint8:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Uint8).Asc())
		case field.Uint16:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Uint16).Asc())
		case field.Uint32:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Uint32).Asc())
		case field.Uint64:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Uint64).Asc())
		case field.Float32:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Float32).Asc())
		case field.Float64:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Float64).Asc())
		case field.Time:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Time).Asc())
		case field.String:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.String).Asc())
		default:
			panic("field type not supported")
		}
	}
	return qb
}

func (qb *QueryBuilder) OrderByDesc(fieldVs ...any) *QueryBuilder {
	for i := range fieldVs {
		fieldV := fieldVs[i]
		switch fieldV.(type) {
		case field.Int:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Int).Desc())
		case field.Int8:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Int8).Desc())
		case field.Int16:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Int16).Desc())
		case field.Int32:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Int32).Desc())
		case field.Int64:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Int64).Desc())
		case field.Uint:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Uint).Desc())
		case field.Uint8:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Uint8).Desc())
		case field.Uint16:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Uint16).Desc())
		case field.Uint32:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Uint32).Desc())
		case field.Uint64:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Uint64).Desc())
		case field.Float32:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Float32).Desc())
		case field.Float64:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Float64).Desc())
		case field.Time:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.Time).Desc())
		case field.String:
			qb.queryWrapper.OrderBy = append(qb.queryWrapper.OrderBy, fieldV.(field.String).Desc())
		default:
			panic("field type not supported")
		}
	}
	return qb
}

// Eq 等于条件
func (ob *OrBuilder) Eq(fieldV any, value any) *OrBuilder {
	switch field := fieldV.(type) {
	case field.Bool:
		ob.addExpr(field.Is(value.(bool)))
	case field.Int:
		ob.addExpr(field.Eq(value.(int)))
	case field.Int8:
		ob.addExpr(field.Eq(value.(int8)))
	case field.Int16:
		ob.addExpr(field.Eq(value.(int16)))
	case field.Int32:
		ob.addExpr(field.Eq(value.(int32)))
	case field.Int64:
		ob.addExpr(field.Eq(value.(int64)))
	case field.Uint:
		ob.addExpr(field.Eq(value.(uint)))
	case field.Uint8:
		ob.addExpr(field.Eq(value.(uint8)))
	case field.Uint16:
		ob.addExpr(field.Eq(value.(uint16)))
	case field.Uint32:
		ob.addExpr(field.Eq(value.(uint32)))
	case field.Uint64:
		ob.addExpr(field.Eq(value.(uint64)))
	case field.Float32:
		ob.addExpr(field.Eq(value.(float32)))
	case field.Float64:
		ob.addExpr(field.Eq(value.(float64)))
	case field.String:
		ob.addExpr(field.Eq(value.(string)))
	case field.Time:
		ob.addExpr(field.Eq(value.(time.Time)))
	default:
		panic("field type not supported")
	}
	return ob
}

// Ne 不等于条件
func (ob *OrBuilder) Ne(fieldV any, value any) *OrBuilder {
	switch field := fieldV.(type) {
	case field.Bool:
		ob.addExpr(field.Not().Is(value.(bool)))
	case field.Int:
		ob.addExpr(field.Neq(value.(int)))
	case field.Int8:
		ob.addExpr(field.Neq(value.(int8)))
	case field.Int16:
		ob.addExpr(field.Neq(value.(int16)))
	case field.Int32:
		ob.addExpr(field.Neq(value.(int32)))
	case field.Int64:
		ob.addExpr(field.Neq(value.(int64)))
	case field.Uint:
		ob.addExpr(field.Neq(value.(uint)))
	case field.Uint8:
		ob.addExpr(field.Neq(value.(uint8)))
	case field.Uint16:
		ob.addExpr(field.Neq(value.(uint16)))
	case field.Uint32:
		ob.addExpr(field.Neq(value.(uint32)))
	case field.Uint64:
		ob.addExpr(field.Neq(value.(uint64)))
	case field.Float32:
		ob.addExpr(field.Neq(value.(float32)))
	case field.Float64:
		ob.addExpr(field.Neq(value.(float64)))
	case field.String:
		ob.addExpr(field.Neq(value.(string)))
	case field.Time:
		ob.addExpr(field.Neq(value.(time.Time)))
	default:
		panic("field type not supported")
	}
	return ob
}

// Gt 大于条件
func (ob *OrBuilder) Gt(fieldV any, value any) *OrBuilder {
	switch field := fieldV.(type) {
	case field.Int:
		ob.addExpr(field.Gt(value.(int)))
	case field.Int8:
		ob.addExpr(field.Gt(value.(int8)))
	case field.Int16:
		ob.addExpr(field.Gt(value.(int16)))
	case field.Int32:
		ob.addExpr(field.Gt(value.(int32)))
	case field.Int64:
		ob.addExpr(field.Gt(value.(int64)))
	case field.Uint:
		ob.addExpr(field.Gt(value.(uint)))
	case field.Uint8:
		ob.addExpr(field.Gt(value.(uint8)))
	case field.Uint16:
		ob.addExpr(field.Gt(value.(uint16)))
	case field.Uint32:
		ob.addExpr(field.Gt(value.(uint32)))
	case field.Uint64:
		ob.addExpr(field.Gt(value.(uint64)))
	case field.Float32:
		ob.addExpr(field.Gt(value.(float32)))
	case field.Float64:
		ob.addExpr(field.Gt(value.(float64)))
	case field.String:
		ob.addExpr(field.Gt(value.(string)))
	case field.Time:
		ob.addExpr(field.Gt(value.(time.Time)))
	default:
		panic("field type not supported")
	}
	return ob
}

// Gte 大于等于条件
func (ob *OrBuilder) Gte(fieldV any, value any) *OrBuilder {
	switch field := fieldV.(type) {
	case field.Int:
		ob.addExpr(field.Gte(value.(int)))
	case field.Int8:
		ob.addExpr(field.Gte(value.(int8)))
	case field.Int16:
		ob.addExpr(field.Gte(value.(int16)))
	case field.Int32:
		ob.addExpr(field.Gte(value.(int32)))
	case field.Int64:
		ob.addExpr(field.Gte(value.(int64)))
	case field.Uint:
		ob.addExpr(field.Gte(value.(uint)))
	case field.Uint8:
		ob.addExpr(field.Gte(value.(uint8)))
	case field.Uint16:
		ob.addExpr(field.Gte(value.(uint16)))
	case field.Uint32:
		ob.addExpr(field.Gte(value.(uint32)))
	case field.Uint64:
		ob.addExpr(field.Gte(value.(uint64)))
	case field.Float32:
		ob.addExpr(field.Gte(value.(float32)))
	case field.Float64:
		ob.addExpr(field.Gte(value.(float64)))
	case field.String:
		ob.addExpr(field.Gte(value.(string)))
	case field.Time:
		ob.addExpr(field.Gte(value.(time.Time)))
	default:
		panic("field type not supported")
	}
	return ob
}

// Lt 小于条件
func (ob *OrBuilder) Lt(fieldV any, value any) *OrBuilder {
	switch field := fieldV.(type) {
	case field.Int:
		ob.addExpr(field.Lt(value.(int)))
	case field.Int8:
		ob.addExpr(field.Lt(value.(int8)))
	case field.Int16:
		ob.addExpr(field.Lt(value.(int16)))
	case field.Int32:
		ob.addExpr(field.Lt(value.(int32)))
	case field.Int64:
		ob.addExpr(field.Lt(value.(int64)))
	case field.Uint:
		ob.addExpr(field.Lt(value.(uint)))
	case field.Uint8:
		ob.addExpr(field.Lt(value.(uint8)))
	case field.Uint16:
		ob.addExpr(field.Lt(value.(uint16)))
	case field.Uint32:
		ob.addExpr(field.Lt(value.(uint32)))
	case field.Uint64:
		ob.addExpr(field.Lt(value.(uint64)))
	case field.Float32:
		ob.addExpr(field.Lt(value.(float32)))
	case field.Float64:
		ob.addExpr(field.Lt(value.(float64)))
	case field.String:
		ob.addExpr(field.Lt(value.(string)))
	case field.Time:
		ob.addExpr(field.Lt(value.(time.Time)))
	default:
		panic("field type not supported")
	}
	return ob
}

// Lte 小于等于条件
func (ob *OrBuilder) Lte(fieldV any, value any) *OrBuilder {
	switch field := fieldV.(type) {
	case field.Int:
		ob.addExpr(field.Lte(value.(int)))
	case field.Int8:
		ob.addExpr(field.Lte(value.(int8)))
	case field.Int16:
		ob.addExpr(field.Lte(value.(int16)))
	case field.Int32:
		ob.addExpr(field.Lte(value.(int32)))
	case field.Int64:
		ob.addExpr(field.Lte(value.(int64)))
	case field.Uint:
		ob.addExpr(field.Lte(value.(uint)))
	case field.Uint8:
		ob.addExpr(field.Lte(value.(uint8)))
	case field.Uint16:
		ob.addExpr(field.Lte(value.(uint16)))
	case field.Uint32:
		ob.addExpr(field.Lte(value.(uint32)))
	case field.Uint64:
		ob.addExpr(field.Lte(value.(uint64)))
	case field.Float32:
		ob.addExpr(field.Lte(value.(float32)))
	case field.Float64:
		ob.addExpr(field.Lte(value.(float64)))
	case field.String:
		ob.addExpr(field.Lte(value.(string)))
	case field.Time:
		ob.addExpr(field.Lte(value.(time.Time)))
	default:
		panic("field type not supported")
	}
	return ob
}

// Like 模糊查询
func (ob *OrBuilder) Like(fieldV any, value string) *OrBuilder {
	switch field := fieldV.(type) {
	case field.String:
		ob.addExpr(field.Like(value))
	default:
		panic("field type not supported")
	}
	return ob
}

// NotLike 模糊查询取反
func (ob *OrBuilder) NotLike(fieldV any, value string) *OrBuilder {
	switch field := fieldV.(type) {
	case field.String:
		ob.addExpr(field.NotLike(value))
	default:
		panic("field type not supported")
	}
	return ob
}

// In 包含查询
func (ob *OrBuilder) In(fieldV any, value any) *OrBuilder {
	switch field := fieldV.(type) {
	case field.Int:
		slice, err := convertToSlice[int](value)
		if err != nil {
			panic(err)
		}
		ob.addExpr(field.In(slice...))
	case field.Int8:
		slice, err := convertToSlice[int8](value)
		if err != nil {
			panic(err)
		}
		ob.addExpr(field.In(slice...))
	case field.Int16:
		slice, err := convertToSlice[int16](value)
		if err != nil {
			panic(err)
		}
		ob.addExpr(field.In(slice...))
	case field.Int32:
		slice, err := convertToSlice[int32](value)
		if err != nil {
			panic(err)
		}
		ob.addExpr(field.In(slice...))
	case field.Int64:
		slice, err := convertToSlice[int64](value)
		if err != nil {
			panic(err)
		}
		ob.addExpr(field.In(slice...))
	case field.Uint:
		slice, err := convertToSlice[uint](value)
		if err != nil {
			panic(err)
		}
		ob.addExpr(field.In(slice...))
	case field.Uint8:
		slice, err := convertToSlice[uint8](value)
		if err != nil {
			panic(err)
		}
		ob.addExpr(field.In(slice...))
	case field.Uint16:
		slice, err := convertToSlice[uint16](value)
		if err != nil {
			panic(err)
		}
		ob.addExpr(field.In(slice...))
	case field.Uint32:
		slice, err := convertToSlice[uint32](value)
		if err != nil {
			panic(err)
		}
		ob.addExpr(field.In(slice...))
	case field.Uint64:
		slice, err := convertToSlice[uint64](value)
		if err != nil {
			panic(err)
		}
		ob.addExpr(field.In(slice...))
	case field.String:
		slice, err := convertToSlice[string](value)
		if err != nil {
			panic(err)
		}
		ob.addExpr(field.In(slice...))
	default:
		panic("field type not supported")
	}
	return ob
}

// convertToSlice 将 interface{} 转换为切片
func convertToSlice[T any](values any) ([]T, error) {
	if valueList, ok := values.([]T); ok {
		return valueList, nil
	}
	return nil, fmt.Errorf("failed to convert %v to []%s", values, reflect.TypeOf((*T)(nil)).Elem())
}
