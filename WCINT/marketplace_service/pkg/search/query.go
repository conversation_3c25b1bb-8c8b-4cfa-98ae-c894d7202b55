package search

import (
	"database/sql/driver"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"gorm.io/datatypes"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm/schema"
	"reflect"
	"strings"
	"time"
)

const (
	// QueryTag tag标记
	QueryTag = "search"
)

type General struct {
	value any
}

func (v General) Value() (driver.Value, error) {
	return GeneralConverter{}.ConvertValue(v.value)
}

type GeneralConverter struct{}

func (c GeneralConverter) ConvertValue(v any) (driver.Value, error) {
	if v == nil {
		return nil, fmt.Errorf("nil value not allowed")
	}
	return driver.DefaultParameterConverter.ConvertValue(v)
}

type DateTime time.Time

func (date DateTime) Value() (driver.Value, error) {
	return time.Time(date), nil
}

func (date DateTime) In(loc *time.Location) time.Time {
	if loc == nil {
		panic("time: missing Location in call to Time.In")
	}
	return time.Time(date).In(loc)
}

type GeneralTable struct {
	tableName string
}

func (gt GeneralTable) TableName() string {
	return gt.tableName
}

type Condition interface {
	SetWhere(c ...gen.Condition)
	SetOrder(o field.Expr)
	SetJoinOn(tabler schema.Tabler, expr field.Expr)
}

type GormCondition struct {
	GormWhere
	GormOrder
	Join []*GormJoin
}
type GormWhere struct {
	Where []gen.Condition
}

type GormOrder struct {
	Order []field.Expr
}

type GormJoin struct {
	Tabler schema.Tabler
	Expr   []field.Expr
}

func (e *GormWhere) SetWhere(c ...gen.Condition) {
	if e.Where == nil {
		e.Where = make([]gen.Condition, 0)
	}
	e.Where = append(e.Where, c...)
}

func (e *GormOrder) SetOrder(o field.Expr) {
	if e.Order == nil {
		e.Order = make([]field.Expr, 0)
	}
	e.Order = append(e.Order, o)
}

func (e *GormCondition) SetJoinOn(tabler schema.Tabler, expr field.Expr) {
	if e.Join == nil {
		e.Join = make([]*GormJoin, 0)
	}
	isExists := false
	for _, v := range e.Join {
		if v.Tabler.TableName() == tabler.TableName() {
			if v.Expr == nil {
				v.Expr = make([]field.Expr, 0)
			}
			v.Expr = append(v.Expr, expr)
			isExists = true
			break
		}
	}
	if !isExists {
		exprs := make([]field.Expr, 0)
		exprs = append(exprs, expr)
		join := &GormJoin{
			Tabler: tabler,
			Expr:   exprs,
		}
		e.Join = append(e.Join, join)
	}
}

func BuildSearchQuery(q interface{}, condition Condition) {
	qType := reflect.TypeOf(q)
	qValue := reflect.ValueOf(q)

	// 处理指针类型
	if qValue.Kind() == reflect.Ptr {
		qValue = qValue.Elem()
		qType = qType.Elem()
	}

	// 检查是否为结构体
	if qValue.Kind() != reflect.Struct {
		panic("BuildSearchQuery: 输入参数不是结构体或结构体指针")
	}

	var tag string
	var ok bool
	var t *searchTag
	for i := 0; i < qType.NumField(); i++ {
		tag, ok = "", false
		tag, ok = qType.Field(i).Tag.Lookup(QueryTag)
		if !ok {
			continue
		}
		switch tag {
		case "-":
			continue
		}
		t = makeTag(tag)
		if qValue.Field(i).IsZero() {
			continue
		}
		column := field.NewField(t.Table, t.Column)
		switch t.Type {
		case "eq":
			condition.SetWhere(column.Eq(General{qValue.Field(i).Interface()}))
		case "neq":
			condition.SetWhere(column.Neq(General{qValue.Field(i).Interface()}))
		case "like":
			condition.SetWhere(column.Like(General{"%" + qValue.Field(i).String() + "%"}))
		case "gt":
			valueInterface := qValue.Field(i).Interface()
			switch qValue.Field(i).Type().String() {
			case "time.Time":
				if timeValue, ok := valueInterface.(time.Time); ok {
					condition.SetWhere(column.Gt(DateTime(timeValue)))
				} else {
					condition.SetWhere(column.Gt(General{valueInterface}))
				}
				break
			default:
				condition.SetWhere(column.Gt(General{valueInterface}))
				break
			}
		case "gte":
			valueInterface := qValue.Field(i).Interface()
			switch qValue.Field(i).Type().String() {
			case "time.Time":
				if timeValue, ok := valueInterface.(time.Time); ok {
					condition.SetWhere(column.Gte(DateTime(timeValue)))
				} else {
					condition.SetWhere(column.Gte(General{valueInterface}))
				}
				break
			default:
				condition.SetWhere(column.Gte(General{valueInterface}))
				break
			}
		case "lt":
			valueInterface := qValue.Field(i).Interface()
			switch qValue.Field(i).Type().String() {
			case "time.Time":
				if timeValue, ok := valueInterface.(time.Time); ok {
					condition.SetWhere(column.Lt(DateTime(timeValue)))
				} else {
					condition.SetWhere(column.Lt(General{valueInterface}))
				}
				break
			default:
				condition.SetWhere(column.Lt(General{valueInterface}))
				break
			}
		case "lte":
			valueInterface := qValue.Field(i).Interface()
			switch qValue.Field(i).Type().String() {
			case "time.Time":
				if timeValue, ok := valueInterface.(time.Time); ok {
					condition.SetWhere(column.Lte(DateTime(timeValue)))
				} else {
					condition.SetWhere(column.Lte(General{valueInterface}))
				}
				break
			default:
				condition.SetWhere(column.Lte(General{valueInterface}))
				break
			}
		case "in":
			var valuers []driver.Valuer
			valueType := qValue.Field(i).Type()
			if valueType.Kind() == reflect.Slice {
				values := qValue.Field(i)
				for i := 0; i < values.Len(); i++ {
					valuers = append(valuers, General{values.Index(i).Interface()})
				}
			}
			condition.SetWhere(column.In(valuers...))
		case "isnull":
			condition.SetWhere(column.IsNull())
		case "json_contains":
			valueType := qValue.Field(i).Type()
			if valueType.Kind() == reflect.Slice {
				values := qValue.Field(i)
				for i := 0; i < values.Len(); i++ {
					conditions := gen.Cond(datatypes.JSONArrayQuery(t.Column).Contains(values.Index(i).Interface()))
					condition.SetWhere(conditions...)
				}
			} else {
				conditions := gen.Cond(datatypes.JSONArrayQuery(t.Column).Contains(qValue.Field(i).Interface()))
				condition.SetWhere(conditions...)
			}
		case "json_overlaps":
			fieldValue := qValue.Field(i).Interface()
			valueStr, _ := convertor.ToJson(fieldValue)
			conditions := gen.Cond(datatypes.JSONOverlaps(column, valueStr))
			condition.SetWhere(conditions...)
		case "order":
			switch strings.ToLower(qValue.Field(i).String()) {
			case "desc":
				condition.SetOrder(column.Desc())
				break
			case "asc":
				condition.SetOrder(column)
				break
			}
		case "join":
			tabler := GeneralTable{t.Join}
			on := field.NewField(t.Join, t.On)
			condition.SetJoinOn(tabler, on.EqCol(column))
			BuildSearchQuery(qValue.Field(i).Interface(), condition)
		}
	}
}

type searchTag struct {
	Type   string
	Column string
	Table  string
	Join   string
	On     string
}

// makeTag 解析search的tag标签
func makeTag(tag string) *searchTag {
	r := &searchTag{}
	tags := strings.Split(tag, ";")
	var ts []string
	for _, t := range tags {
		ts = strings.Split(t, ":")
		if len(ts) == 0 {
			continue
		}
		switch ts[0] {
		case "type":
			if len(ts) > 1 {
				r.Type = ts[1]
			}
		case "column":
			if len(ts) > 1 {
				r.Column = ts[1]
			}
		case "table":
			if len(ts) > 1 {
				r.Table = ts[1]
			}
		case "on":
			if len(ts) > 1 {
				r.On = ts[1]
			}
		case "join":
			if len(ts) > 1 {
				r.Join = ts[1]
			}
		}

	}
	return r
}
