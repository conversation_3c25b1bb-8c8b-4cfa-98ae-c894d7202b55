package pagination

import "gorm.io/gorm"

type IPagination interface {
	GetPage() int
	GetPageSize() int
	GetOffset() int
}
type Pagination struct {
	Page     int `form:"page" json:"page" binding:"required"`           // 页码
	PageSize int `form:"page_size" json:"page_size" binding:"required"` // 每页显示的条目数量
}

func (m *Pagination) GetPage() int {
	if m.Page <= 0 {
		m.Page = 1
	}
	return m.Page
}

func (m *Pagination) GetPageSize() int {
	if m.PageSize <= 0 {
		m.PageSize = 10
	}
	return m.PageSize
}

func (m *Pagination) GetOffset() int {
	return (m.Page - 1) * m.PageSize
}

func Paginate(pages Pagination, export bool) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if !export {
			switch {
			case pages.PageSize > 100:
				pages.PageSize = 100
			case pages.PageSize <= 0:
				pages.PageSize = 10
			}
		}

		offset := (pages.Page - 1) * pages.PageSize
		return db.Offset(offset).Limit(pages.PageSize)
	}
}
