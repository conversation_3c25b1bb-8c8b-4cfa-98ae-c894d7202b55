package cache

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"marketplace_service/global"
	"marketplace_service/pkg/utils"
	"time"
)

// GetFromCache 通用缓存工具函数
func GetFromCache(ctx context.Context, cacheKey string, target interface{}) error {
	result, err := global.REDIS.Get(ctx, cacheKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return err
		}
		log.Ctx(ctx).Errorf("Redis 获取数据失败 key:%s, err:%v", cacheKey, err)
		return fmt.Errorf("缓存服务异常")
	}
	if result == "null" {
		return response.SystemErr.SetMsg("数据不存在")
	}
	if err := utils.MsgpackUnmarshal([]byte(result), target); err != nil {
		log.Ctx(ctx).Errorf("缓存数据解析失败 key:%s, err:%v", cacheKey, err)
		return fmt.Errorf("缓存数据格式异常")
	}
	return nil
}

// SetToCache 通用缓存工具函数
func SetToCache(ctx context.Context, cacheKey string, value interface{}, ttl time.Duration) error {
	cacheData, err := utils.MsgpackMarshal(value)
	if err != nil {
		return fmt.Errorf("序列化缓存数据失败: %v", err)
	}
	if err := global.REDIS.Set(ctx, cacheKey, cacheData, ttl).Err(); err != nil {
		log.Ctx(ctx).Errorf("Redis 写入数据失败 key:%s, err:%v", cacheKey, err)
		return fmt.Errorf("缓存服务异常")
	}
	return nil
}
