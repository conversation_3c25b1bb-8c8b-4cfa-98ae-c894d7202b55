package utils

// FindIntInSlice 判断是否存在于切片内(int)
func FindIntInSlice(v int32, s []int32) bool {
	for _, sv := range s {
		if sv == v {
			return true
		}
	}
	return false
}

// Slice2KeyMap 将切片转为指定字段为key的map
func Slice2KeyMap[T any, K comparable](slice []T, keyFunc func(T) K) map[K]T {
	result := make(map[K]T)
	for _, item := range slice {
		key := keyFunc(item)
		result[key] = item
	}
	return result
}
