package utils

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
)

// GenerateSign 生成签名
func GenerateSign(params map[string]interface{}, secret string) (string, error) {
	// 参数排序
	sortedKeys := KeySort(params)

	// 拼接用于签名的字符串
	str4Sign, err := GenerateString4Sign(sortedKeys, params, secret)
	if err != nil {
		return "", err
	}

	// 生成签名
	return GenerateMD5Sign(str4Sign), nil
}

// KeySort 将参数key按ascii码排序
func KeySort(params map[string]interface{}) []string {
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	return keys
}

// GenerateString4Sign 生成用于产生签名的字符串
func GenerateString4Sign(keys []string, params map[string]interface{}, secret string) (string, error) {
	str4Sign := secret

	for _, k := range keys {
		// 跳过sign字段
		if k == "sign" {
			continue
		}

		// 处理不同类型的参数值
		switch v := params[k].(type) {
		case string:
			str4Sign += k + v
		case float64:
			str4Sign += k + strconv.FormatFloat(v, 'f', -1, 64)
		case bool:
			str4Sign += k + strconv.FormatBool(v)
		case nil:
			str4Sign += k + "null"
		default:
			// 尝试JSON序列化
			jsonBytes, err := json.Marshal(v)
			if err != nil {
				return "", fmt.Errorf("无法序列化参数 %s: %v", k, err)
			}
			str4Sign += k + string(jsonBytes)
		}
	}

	str4Sign += secret
	return str4Sign, nil
}

// GenerateMD5Sign 生成MD5签名
func GenerateMD5Sign(str4Sign string) string {
	hasher := md5.New()
	hasher.Write([]byte(str4Sign))
	return hex.EncodeToString(hasher.Sum(nil))
}
