package utils

import (
	"gopkg.in/mgo.v2/bson"
	"path"
)

// GetImageName get image name
func GetImageName(name string) string {
	ext := path.Ext(name)
	return bson.NewObjectId().Hex() + ext
}

// GetGifWidthHeight 获取 GIF 图片的宽高
func GetGifWidthHeight(imgBytes []byte) (int, int) {
	version := string(imgBytes[:6])
	if version != "GIF87a" && version != "GIF89a" {
		return 0, 0
	}
	width := int(imgBytes[6]) + int(imgBytes[7])<<8
	height := int(imgBytes[8]) + int(imgBytes[9])<<8
	return width, height
}
