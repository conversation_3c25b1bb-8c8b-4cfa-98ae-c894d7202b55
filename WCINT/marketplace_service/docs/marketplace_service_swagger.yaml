definitions:
  define.AddMallItemReq:
    properties:
      daily_limit:
        description: 每日限购数量
        minimum: 0
        type: integer
      discount:
        description: 折扣
        maximum: 100
        minimum: 1
        type: integer
      freight:
        description: 运费(分)
        minimum: 0
        type: integer
      item_id:
        description: 商品ID
        type: string
      priority:
        description: 优先级
        maximum: 9999
        minimum: 0
        type: integer
      sale_price:
        description: 售价(分)
        minimum: 1
        type: integer
      sku_id:
        description: 商品 sku_id
        type: string
      start_time:
        description: 开始展示时间
        type: string
      stock:
        description: 限定库存量
        minimum: 1
        type: integer
      stock_type:
        description: 库存类型（1=限定库存，2=同步商品库存）
        type: integer
      total_limit:
        description: 总限购数量
        minimum: 0
        type: integer
    required:
    - daily_limit
    - freight
    - item_id
    - sale_price
    - sku_id
    - start_time
    - stock
    - stock_type
    - total_limit
    type: object
  define.AddMallItemResp:
    properties:
      id:
        description: 直购商品ID
        example: "0"
        type: string
    type: object
  define.AdminGetOrderPrivateInfoResp:
    properties:
      consignee_phone:
        description: 收货人电话
        type: string
    type: object
  define.CancelAdminTradeOrderReq:
    properties:
      id:
        description: 订单id
        example: "0"
        type: string
    required:
    - id
    type: object
  define.CancelAdminTradeOrderResp:
    type: object
  define.CancelTradeOrderReq:
    properties:
      id:
        description: 订单id
        example: "0"
        type: string
    required:
    - id
    type: object
  define.CancelTradeOrderResp:
    type: object
  define.ConfirmTradeOrderReq:
    properties:
      id:
        description: 订单id
        example: "0"
        type: string
    required:
    - id
    type: object
  define.ConfirmTradeOrderResp:
    type: object
  define.DelTradeOrderReq:
    properties:
      id:
        description: 订单id
        example: "0"
        type: string
    required:
    - id
    type: object
  define.DelTradeOrderResp:
    type: object
  define.EditMallItemPriorityReq:
    properties:
      id:
        description: ID
        example: "0"
        type: string
      priority:
        description: 优先级
        maximum: 9999
        minimum: 1
        type: integer
    required:
    - id
    type: object
  define.EditMallItemPriorityResp:
    properties:
      id:
        description: 直购商品ID
        example: "0"
        type: string
    type: object
  define.EditMallItemReq:
    properties:
      daily_limit:
        description: 每日限购数量
        minimum: 0
        type: integer
      discount:
        description: 折扣
        maximum: 100
        minimum: 1
        type: integer
      freight:
        description: 运费(分)
        minimum: 0
        type: integer
      id:
        description: 直购商品ID
        example: "0"
        type: string
      item_id:
        description: 商品ID
        type: string
      priority:
        description: 优先级
        maximum: 9999
        minimum: 0
        type: integer
      sale_price:
        description: 售价(分)
        minimum: 1
        type: integer
      sku_id:
        description: 商品 sku_id
        type: string
      start_time:
        description: 开始展示时间
        type: string
      stock:
        description: 限定库存量
        minimum: 1
        type: integer
      stock_type:
        description: 库存类型（1=限定库存，2=同步商品库存）
        type: integer
      total_limit:
        description: 总限购数量
        minimum: 0
        type: integer
    required:
    - daily_limit
    - freight
    - id
    - item_id
    - sale_price
    - sku_id
    - start_time
    - stock
    - stock_type
    - total_limit
    type: object
  define.EditMallItemResp:
    properties:
      id:
        description: 直购商品ID
        example: "0"
        type: string
    type: object
  define.EditMallItemStatusReq:
    properties:
      id:
        description: ID
        example: "0"
        type: string
      status:
        description: 状态（0=待上架，1=已上架，2=已下架）
        minimum: 0
        type: integer
    required:
    - id
    - status
    type: object
  define.EditMallItemStatusResp:
    properties:
      id:
        description: 直购商品ID
        example: "0"
        type: string
    type: object
  define.GetAdminTradeOrderData:
    properties:
      created_at:
        description: 创建时间
        type: string
      freight_amount:
        description: 运费
        type: integer
      id:
        description: 订单ID
        example: "0"
        type: string
      order_items:
        description: 订单子单列表
        items:
          $ref: '#/definitions/define.GetAdminTradeOrderItemData'
        type: array
      order_status:
        description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        type: integer
      pay_amount:
        description: 支付金额
        type: integer
      total_amount:
        description: 总金额
        type: integer
      user_id:
        description: 用户ID
        type: string
      user_nickname:
        description: 用户昵称
        type: string
    type: object
  define.GetAdminTradeOrderDetailFreight:
    properties:
      delivered_at:
        description: 发货时间
        type: string
      delivery_company:
        description: 快递公司
        type: string
      delivery_number:
        description: 快递单号
        type: string
      order_item_id:
        description: 订单子单ID
        example: "0"
        type: string
      records:
        description: 物流信息
        items:
          type: integer
        type: array
    type: object
  define.GetAdminTradeOrderDetailResp:
    properties:
      cancel_at:
        description: 取消时间
        type: string
      cancel_type:
        description: 取消类型（1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）
        type: integer
      consignee_address:
        description: 收货地址
        items:
          type: integer
        type: array
      consignee_name:
        description: 收货人姓名
        type: string
      consignee_phone:
        description: 收货人电话
        type: string
      created_at:
        description: 下单时间
        type: string
      delivered_at:
        description: 发货时间
        type: string
      discount_amount:
        description: 折扣金额
        type: integer
      finished_at:
        description: 完成时间
        type: string
      freight_amount:
        description: 运费
        type: integer
      freight_infos:
        description: 物流信息
        items:
          $ref: '#/definitions/define.GetAdminTradeOrderDetailFreight'
        type: array
      id:
        description: 订单ID
        example: "0"
        type: string
      order_items:
        description: 物品列表
        items:
          $ref: '#/definitions/define.GetAdminTradeOrderItemData'
        type: array
      order_status:
        description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        type: integer
      order_type:
        description: 订单类型（1=直购订单）
        type: integer
      pay_amount:
        description: 支付金额
        type: integer
      pay_order_id:
        description: 支付订单ID
        type: string
      payment_method:
        description: 支付方式
        type: string
      payment_status:
        description: 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
        type: integer
      payment_time:
        description: 支付时间
        type: string
      shipping_status:
        description: 发货状态（0=未发货，1=已发货，2=已签收）
        type: integer
      supply_chain_order_id:
        description: 供应链ID
        example: ""
        type: string
      terminal:
        description: 订单渠道
        type: string
      total_amount:
        description: 总金额
        type: integer
      user_id:
        description: 用户ID
        type: string
      user_nickname:
        description: 用户昵称
        type: string
      user_remark:
        description: 用户备注
        type: string
    type: object
  define.GetAdminTradeOrderItemData:
    properties:
      brand_name:
        description: 商品品牌名称
        type: string
      category_id:
        description: 商品分类id
        type: string
      category_name:
        description: 商品分类名称
        type: string
      discount_amount:
        description: 商品享受的优惠金额
        type: integer
      freight_amount:
        description: 运费
        type: integer
      id:
        description: 直购商品ID
        example: "0"
        type: string
      ip_id:
        description: 商品 ip_id
        type: string
      ip_name:
        description: 商品IP名称
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品属性
        type: string
      quantity:
        description: 购买数量
        type: integer
      sale_price:
        description: 单价
        type: integer
      sku_id:
        description: 商品 sku_id
        type: string
      trademark_id:
        description: 商品品牌id
        type: string
    type: object
  define.GetAdminTradeOrderListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetAdminTradeOrderData'
        type: array
      total:
        type: integer
    type: object
  define.GetTradeOrderDetailResp:
    properties:
      pay_amount:
        description: 支付金额(单位:分)
        type: integer
      payment_status:
        description: 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
        type: integer
    type: object
  define.GetWebTradeOrderData:
    properties:
      countdown_second:
        description: 倒计时秒数
        type: integer
      freight_info:
        description: 物流信息
        items:
          $ref: '#/definitions/define.GetWebTradeOrderDetailFreight'
        type: array
      id:
        description: id
        example: "0"
        type: string
      order_items:
        description: 物品列表
        items:
          $ref: '#/definitions/define.GetWebTradeOrderItemData'
        type: array
      order_status:
        description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        type: integer
      pay_amount:
        description: 支付金额
        type: integer
      payment_status:
        description: 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
        type: integer
      shipping_status:
        description: 发货状态（0=未发货，1=已发货，2=已签收）
        type: integer
      total_amount:
        description: 总金额
        type: integer
    type: object
  define.GetWebTradeOrderDetailFreight:
    properties:
      delivery_company:
        description: 快递公司
        type: string
      delivery_number:
        description: 快递单号
        type: string
    type: object
  define.GetWebTradeOrderDetailResp:
    properties:
      cancel_type:
        description: 取消类型（1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）
        type: integer
      consignee_address:
        description: 收货地址
        items:
          type: integer
        type: array
      consignee_name:
        description: 收货人姓名
        type: string
      consignee_phone:
        description: 收货人电话
        type: string
      countdown_second:
        description: 倒计时秒数
        type: integer
      created_at:
        description: 下单时间
        type: string
      discount_amount:
        description: 折扣金额
        type: integer
      finished_second:
        description: 订单完成倒计时秒数
        type: integer
      freight_amount:
        description: 运费
        type: integer
      freight_info:
        description: 物流信息
        items:
          $ref: '#/definitions/define.GetWebTradeOrderDetailFreight'
        type: array
      id:
        description: id
        example: "0"
        type: string
      order_items:
        description: 物品列表
        items:
          $ref: '#/definitions/define.GetWebTradeOrderItemData'
        type: array
      order_status:
        description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        type: integer
      pay_amount:
        description: 支付金额
        type: integer
      payment_method:
        description: 支付方式
        type: string
      payment_status:
        description: 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
        type: integer
      payment_time:
        description: 支付时间
        type: string
      shipping_status:
        description: 发货状态（0=未发货，1=已发货，2=已签收）
        type: integer
      total_amount:
        description: 总金额
        type: integer
      user_remark:
        description: 用户备注
        type: string
    type: object
  define.GetWebTradeOrderFreightInfoResp:
    properties:
      checked:
        description: 0未签收 1已签收
        type: integer
      delivery_company:
        description: 快递公司
        type: string
      delivery_number:
        description: 快递单号
        type: string
      records:
        items:
          type: integer
        type: array
      status:
        type: integer
    type: object
  define.GetWebTradeOrderItemData:
    properties:
      id:
        description: 直购商品ID
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品属性
        type: string
      quantity:
        description: 购买数量
        type: integer
      sale_price:
        description: 单价
        type: integer
    type: object
  define.GetWebTradeOrderListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetWebTradeOrderData'
        type: array
      total:
        type: integer
    type: object
  define.GetWebTradeOrderStatusStatData:
    properties:
      count:
        type: integer
      order_status:
        description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        type: integer
    type: object
  define.GetWebTradeOrderStatusStatResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetWebTradeOrderStatusStatData'
        type: array
    type: object
  define.MallItemDetailResp:
    properties:
      available_stock:
        description: 直购商品可用库存
        type: integer
      category_id:
        description: 商品分类id
        type: string
      created_at:
        description: 创建时间
        type: string
      daily_limit:
        description: 每日限购数量
        type: integer
      discount:
        description: 折扣
        type: integer
      freight:
        description: 运费(分)
        type: integer
      id:
        description: 主键
        example: "0"
        type: string
      ipid:
        description: 商品 ip_id
        type: string
      item_id:
        description: 商品 id
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      priority:
        description: 优先级
        type: integer
      purchase_price:
        description: 进价
        type: integer
      sale_price:
        description: 售价(分)
        type: integer
      sell_listings:
        description: 商品库存
        type: integer
      sku_id:
        description: 商品 sku_id
        type: string
      spu_id:
        description: 商品 spu_id
        type: string
      start_time:
        description: 开始展示时间
        type: string
      status:
        description: 状态（0=待上架，1=已上架，2=已下架）
        type: integer
      stock:
        description: 限定库存量
        type: integer
      stock_type:
        description: 库存类型（1=限定库存，2=同步商品库存）
        type: integer
      total_limit:
        description: 总限购数量
        type: integer
      trademark_id:
        description: 商品品牌id
        type: string
    type: object
  define.MallItemPageData:
    properties:
      available_stock:
        description: 剩余库存
        type: integer
      brand_name:
        description: 商品品牌名称
        type: string
      category_id:
        description: 商品分类ID
        type: string
      category_name:
        description: 商品分类名称
        type: string
      discount:
        description: 折扣
        type: integer
      discount_price:
        description: 折扣价
        type: integer
      freight:
        description: 运费
        type: integer
      id:
        description: 主键
        example: "0"
        type: string
      ip_id:
        description: IP ID
        type: string
      ip_name:
        description: IP名称
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品 ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 规格
        type: string
      priority:
        description: 优先级
        type: integer
      purchase_price:
        description: 进价
        type: integer
      sale_price:
        description: 售价
        type: integer
      sku_id:
        description: SKU ID
        type: string
      spu_id:
        description: SPU ID
        type: string
      start_time:
        description: 开始展示时间
        type: string
      status:
        description: 状态（0=待上架，1=已上架，2=已下架）
        type: integer
      stock:
        description: 销售库存
        type: integer
      trademark_id:
        description: 商品品牌ID
        type: string
      updated_by:
        description: 最后操作人
        type: string
    type: object
  define.MallItemPageResp:
    properties:
      list:
        description: 列表
        items:
          $ref: '#/definitions/define.MallItemPageData'
        type: array
      total:
        description: 总数
        type: integer
    type: object
  define.OpenTriggerDisplayByStartTimeReq:
    type: object
  define.OpenUpdateMallItemNotifyReq:
    properties:
      item_ids:
        items:
          type: string
        type: array
    required:
    - item_ids
    type: object
  define.SupplyChainNoticeReq:
    properties:
      data:
        items:
          additionalProperties: true
          type: object
        type: array
      type:
        type: integer
    required:
    - type
    type: object
  define.SupplyChainNoticeResp:
    properties:
      code:
        type: integer
      data:
        properties:
          failed:
            description: 失败的消息ID（请求参数中的"mid"）
            items:
              type: integer
            type: array
        type: object
      desc:
        type: string
    type: object
  define.SyncAdminTradeOrderFreightReq:
    properties:
      id:
        description: 订单详情ID
        example: "0"
        type: string
    required:
    - id
    type: object
  define.SyncAdminTradeOrderFreightResp:
    type: object
  define.TradeOrderFailedRetryUploadToSupplyReq:
    type: object
  define.TradeOrderFinishReq:
    type: object
  define.TradeOrderFinishResp:
    type: object
  define.TradeOrderPaySuccessReq:
    properties:
      id:
        example: "0"
        type: string
      pay_method:
        type: string
      pay_time:
        type: string
      recharge_order_id:
        type: string
    required:
    - id
    - recharge_order_id
    type: object
  define.TradeOrderPaySuccessResp:
    type: object
  define.TradeOrderRefundReq:
    properties:
      amount:
        type: integer
      recharge_order_id:
        type: string
    required:
    - amount
    - recharge_order_id
    type: object
  define.TradeOrderRefundResp:
    properties:
      amount:
        type: integer
    type: object
  define.TradeOrderTimeoutCloseReq:
    type: object
  define.UpdateTradeOrderAddressReq:
    properties:
      address_id:
        description: 地址ID
        type: string
      id:
        description: 订单id
        example: "0"
        type: string
    required:
    - address_id
    - id
    type: object
  define.UpdateTradeOrderAddressResp:
    type: object
  define.WebBuyReq:
    properties:
      address_id:
        description: 地址ID
        type: string
      mall_item_id:
        description: 直购商品ID
        example: "0"
        type: string
      pay_amount:
        description: 支付金额
        type: integer
      quantity:
        description: 购买数量
        type: integer
      remark:
        description: 备注
        type: string
    required:
    - address_id
    - mall_item_id
    - pay_amount
    - quantity
    type: object
  define.WebBuyResp:
    properties:
      created_at:
        description: 创建时间
        type: string
      is_new_order:
        description: 是否是新订单
        type: boolean
      order_id:
        description: 直购订单ID
        example: "0"
        type: string
      pay_amount:
        description: 应支付金额
        type: integer
    type: object
  define.WebGetMallItemDetailResp:
    properties:
      available_stock:
        description: 剩余库存
        type: integer
      brand_name:
        description: 商品品牌名称
        type: string
      category_id:
        description: 商品分类id
        type: string
      category_name:
        description: 商品分类名称
        type: string
      detail:
        description: 商品详情
        type: string
      discount:
        description: 折扣
        type: integer
      discount_price:
        description: 折扣价
        type: integer
      freight:
        description: 运费
        type: integer
      id:
        description: 直购商品ID
        example: "0"
        type: string
      image_infos:
        description: 商品图片
        items:
          type: string
        type: array
      ip_id:
        description: 商品 ip_id
        type: string
      ip_name:
        description: 商品IP名称
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      sale_price:
        description: 售价
        type: integer
      specs:
        description: 商品规格
        type: string
      status:
        description: 直购商品状态（0=待上架，1=已上架，2=已下架）
        type: integer
      stock:
        description: 总库存
        type: integer
      trademark_id:
        description: 商品品牌id
        type: string
      user_wishlist_status:
        description: 用户心愿单状态
        type: integer
    type: object
  define.WebGetMallItemListResp:
    properties:
      has_more:
        description: 判断当前页是否为最后一页
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.WebMallItemListData'
        type: array
    type: object
  define.WebMallItemListData:
    properties:
      discount:
        description: 折扣
        type: integer
      discount_price:
        description: 折扣价
        type: integer
      id:
        description: 直购商品ID
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      priority:
        description: 优先级
        type: integer
      sale_price:
        description: 售价
        type: integer
      sales:
        description: 销量
        type: integer
      wishlist_count:
        description: 想要数量
        type: integer
    type: object
  define.WebWishlistListData:
    properties:
      discount:
        description: 折扣
        type: integer
      discount_price:
        description: 折扣价
        type: integer
      id:
        description: 用户想要ID
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      mall_item_id:
        description: 直购商品ID
        type: integer
      sale_price:
        description: 售价
        type: integer
    type: object
  define.WebWishlistListResp:
    properties:
      has_more:
        description: 判断当前页是否为最后一页
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.WebWishlistListData'
        type: array
    type: object
  define.WebWishlistReq:
    properties:
      item_id:
        type: string
    required:
    - item_id
    type: object
  response.Data:
    properties:
      code:
        type: integer
      data: {}
      desc:
        type: string
      trace_id:
        description: 链路id
        type: string
    type: object
info:
  contact: {}
  description: 接口文档
  title: app API
  version: 1.0.0
paths:
  /admin/v1/mall_item/add:
    post:
      description: 管理端创建新商品
      parameters:
      - description: 商品信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddMallItemReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddMallItemResp'
              type: object
      security:
      - Bearer: []
      summary: 创建商品
      tags:
      - 管理端-商品管理
  /admin/v1/mall_item/detail:
    get:
      description: 管理端获取商品详细信息
      parameters:
      - example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.MallItemDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取商品详情
      tags:
      - 管理端-商品管理
  /admin/v1/mall_item/edit:
    post:
      description: 管理端更新商品信息
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditMallItemReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditMallItemResp'
              type: object
      security:
      - Bearer: []
      summary: 更新商品
      tags:
      - 管理端-商品管理
  /admin/v1/mall_item/edit_priority:
    post:
      description: 管理端更新商品优先级
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditMallItemPriorityReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditMallItemPriorityResp'
              type: object
      security:
      - Bearer: []
      summary: 更新商品优先级
      tags:
      - 管理端-商品管理
  /admin/v1/mall_item/edit_status:
    post:
      description: 管理端更新商品信息
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditMallItemStatusReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditMallItemStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 更新商品状态
      tags:
      - 管理端-商品管理
  /admin/v1/mall_item/list:
    get:
      description: 管理端获取商品分页列表
      parameters:
      - collectionFormat: csv
        description: 分类
        in: query
        items:
          type: string
        name: category_ids
        type: array
      - description: 直购商品ID
        example: ""
        in: query
        name: id
        type: string
      - collectionFormat: csv
        description: ip
        in: query
        items:
          type: string
        name: ip_ids
        type: array
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 出售价格
        in: query
        name: sale_price_gte
        type: integer
      - description: 出售价格
        in: query
        name: sale_price_lte
        type: integer
      - description: sku_id
        in: query
        name: sku_id
        type: string
      - description: 状态
        in: query
        name: status
        type: integer
      - collectionFormat: csv
        description: 品牌
        in: query
        items:
          type: string
        name: trademark_ids
        type: array
      - description: 操作人
        in: query
        name: updated_by
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.MallItemPageResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 获取商品列表
      tags:
      - 管理端-商品管理
  /admin/v1/trade_order/cancel:
    post:
      description: 取消订单
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.CancelAdminTradeOrderReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.CancelAdminTradeOrderResp'
              type: object
      security:
      - Bearer: []
      summary: 取消订单
      tags:
      - 管理端-订单管理
  /admin/v1/trade_order/detail:
    get:
      description: 管理端获取订单详细信息
      parameters:
      - example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetAdminTradeOrderDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取订单详情
      tags:
      - 管理端-订单管理
  /admin/v1/trade_order/list:
    get:
      description: 管理端获取订单分页列表
      parameters:
      - description: 创建时间开始
        in: query
        name: created_at_gte
        type: string
      - description: 创建时间结束
        in: query
        name: created_at_lte
        type: string
      - description: 订单号
        example: "0"
        in: query
        name: id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 直购商品ID
        example: "0"
        in: query
        name: mall_item_id
        type: string
      - description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        in: query
        name: order_status
        type: integer
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 支付订单ID
        in: query
        name: pay_order_id
        type: string
      - description: 商品 sku_id
        in: query
        name: sku_id
        type: string
      - description: 用户ID
        in: query
        name: user_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetAdminTradeOrderListResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 获取订单列表
      tags:
      - 管理端-订单管理
  /admin/v1/trade_order/private_info:
    get:
      description: 管理端获取订单隐私详情
      parameters:
      - description: 订单id
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AdminGetOrderPrivateInfoResp'
              type: object
      security:
      - Bearer: []
      summary: 获取订单隐私详情
      tags:
      - 管理端-订单管理
  /admin/v1/trade_order/refund:
    post:
      description: 订单发起退款
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TradeOrderRefundReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.TradeOrderRefundResp'
              type: object
      security:
      - Bearer: []
      summary: 订单发起退款
      tags:
      - 管理端-订单管理
  /admin/v1/trade_order/sync_freight:
    post:
      description: 同步订单物流信息
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.SyncAdminTradeOrderFreightReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.SyncAdminTradeOrderFreightResp'
              type: object
      security:
      - Bearer: []
      summary: 同步订单物流信息
      tags:
      - 管理端-订单管理
  /open/v1/mall_item/trigger_display:
    post:
      description: 处理开始展示的直购商品
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.OpenTriggerDisplayByStartTimeReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 处理开始展示的直购商品
      tags:
      - open端-商品管理
  /open/v1/mall_item/update_notify:
    post:
      description: 商品信息变动通知
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.OpenUpdateMallItemNotifyReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 商品信息变动通知
      tags:
      - open端-商品管理
  /open/v1/notice/supply_chain:
    post:
      description: 供应链通知
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.SupplyChainNoticeReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.SupplyChainNoticeResp'
              type: object
      security:
      - Bearer: []
      summary: 供应链通知
      tags:
      - open端-通知管理
  /open/v1/trade_order/detail:
    get:
      description: 获取订单详情
      parameters:
      - example: "0"
        in: query
        name: id
        required: true
        type: string
      - in: query
        name: user_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetTradeOrderDetailResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 获取订单详情
      tags:
      - open端-订单管理
  /open/v1/trade_order/finish:
    post:
      description: 订单支付自动收货
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TradeOrderFinishReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.TradeOrderFinishResp'
              type: object
      security:
      - Bearer: []
      summary: 订单支付自动收货
      tags:
      - open端-订单管理
  /open/v1/trade_order/pay_success:
    post:
      description: 订单支付成功回调
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TradeOrderPaySuccessReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.TradeOrderPaySuccessResp'
              type: object
      security:
      - Bearer: []
      summary: 订单支付成功回调
      tags:
      - open端-订单管理
  /open/v1/trade_order/retry_upload_to_supply:
    post:
      description: 重新上传失败的订单到供应链
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TradeOrderFailedRetryUploadToSupplyReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 重新上传失败的订单到供应链
      tags:
      - open端-订单管理
  /open/v1/trade_order/timeout_close:
    post:
      description: 订单支付超时关闭
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TradeOrderTimeoutCloseReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 订单支付超时关闭
      tags:
      - open端-订单管理
  /web/v1/mall_item/buy:
    post:
      description: 下单购买
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.WebBuyReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.WebBuyResp'
              type: object
      security:
      - Bearer: []
      summary: 下单购买
      tags:
      - 用户端-直购商城
  /web/v1/mall_item/detail:
    get:
      description: 获取直购商品详情
      parameters:
      - description: 直购商品ID
        example: "0"
        in: query
        name: id
        required: true
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.WebGetMallItemDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取直购商品详情
      tags:
      - 用户端-直购商城
  /web/v1/mall_item/list:
    get:
      description: 获取直购商品列表
      parameters:
      - description: 商品 ip_id
        in: query
        name: ip_id
        type: string
      - description: 排序类型 1=综合，2=销量，3=价格
        in: query
        name: order_by
        type: integer
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 排序方式  asc-升序 desc-降序
        in: query
        name: sort_order
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.WebGetMallItemListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取直购商品列表
      tags:
      - 用户端-直购商城
  /web/v1/trade_order/cancel:
    post:
      description: 取消订单
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.CancelTradeOrderReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.CancelTradeOrderResp'
              type: object
      security:
      - Bearer: []
      summary: 取消订单
      tags:
      - 用户端-订单管理
  /web/v1/trade_order/confirm:
    post:
      description: 确定收货
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.ConfirmTradeOrderReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.ConfirmTradeOrderResp'
              type: object
      security:
      - Bearer: []
      summary: 确定收货
      tags:
      - 用户端-订单管理
  /web/v1/trade_order/del:
    post:
      description: 删除订单
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.DelTradeOrderReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.DelTradeOrderResp'
              type: object
      security:
      - Bearer: []
      summary: 删除订单
      tags:
      - 用户端-订单管理
  /web/v1/trade_order/detail:
    get:
      description: 查询直购订单详情
      parameters:
      - description: 订单id
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetWebTradeOrderDetailResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 查询直购订单详情
      tags:
      - 用户端-订单管理
  /web/v1/trade_order/freight_info:
    get:
      description: 查询直购订单物流信息
      parameters:
      - description: 快递单号
        in: query
        name: delivery_number
        required: true
        type: string
      - description: 订单id
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetWebTradeOrderFreightInfoResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 查询直购订单物流信息
      tags:
      - 用户端-订单管理
  /web/v1/trade_order/list:
    get:
      description: 查询直购订单列表
      parameters:
      - description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        in: query
        name: order_status
        type: integer
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebTradeOrderListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询直购订单列表
      tags:
      - 用户端-订单管理
  /web/v1/trade_order/status_stat:
    get:
      description: 查询直购订单状态统计
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetWebTradeOrderStatusStatResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 查询直购订单状态统计
      tags:
      - 用户端-订单管理
  /web/v1/trade_order/update_address:
    post:
      description: 修改地址
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.UpdateTradeOrderAddressReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.UpdateTradeOrderAddressResp'
              type: object
      security:
      - Bearer: []
      summary: 修改地址
      tags:
      - 用户端-订单管理
  /web/v1/user_wishlist/add:
    post:
      description: 添加想要
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.WebWishlistReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 添加想要
      tags:
      - 用户端-想要管理
  /web/v1/user_wishlist/list:
    get:
      description: 用户想要列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.WebWishlistListResp'
              type: object
      security:
      - Bearer: []
      summary: 用户想要列表
      tags:
      - 用户端-想要管理
  /web/v1/user_wishlist/remove:
    post:
      description: 移除想要
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.WebWishlistReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 移除想要
      tags:
      - 用户端-想要管理
securityDefinitions:
  Bearer:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
