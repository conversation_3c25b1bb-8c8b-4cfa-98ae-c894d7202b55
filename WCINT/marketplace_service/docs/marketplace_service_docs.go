// Code generated by swaggo/swag. DO NOT EDIT.

package docs

import "github.com/swaggo/swag"

const docTemplatemarketplace_service = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/v1/mall_item/add": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "管理端创建新商品",
                "tags": [
                    "管理端-商品管理"
                ],
                "summary": "创建商品",
                "parameters": [
                    {
                        "description": "商品信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.AddMallItemReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.AddMallItemResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/v1/mall_item/detail": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "管理端获取商品详细信息",
                "tags": [
                    "管理端-商品管理"
                ],
                "summary": "获取商品详情",
                "parameters": [
                    {
                        "type": "string",
                        "example": "0",
                        "name": "id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.MallItemDetailResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/v1/mall_item/edit": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "管理端更新商品信息",
                "tags": [
                    "管理端-商品管理"
                ],
                "summary": "更新商品",
                "parameters": [
                    {
                        "description": "更新参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.EditMallItemReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.EditMallItemResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/v1/mall_item/edit_priority": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "管理端更新商品优先级",
                "tags": [
                    "管理端-商品管理"
                ],
                "summary": "更新商品优先级",
                "parameters": [
                    {
                        "description": "更新参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.EditMallItemPriorityReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.EditMallItemPriorityResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/v1/mall_item/edit_status": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "管理端更新商品信息",
                "tags": [
                    "管理端-商品管理"
                ],
                "summary": "更新商品状态",
                "parameters": [
                    {
                        "description": "更新参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.EditMallItemStatusReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.EditMallItemStatusResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/v1/mall_item/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "管理端获取商品分页列表",
                "tags": [
                    "管理端-商品管理"
                ],
                "summary": "获取商品列表",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "分类",
                        "name": "category_ids",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "",
                        "description": "直购商品ID",
                        "name": "id",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "ip",
                        "name": "ip_ids",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "商品ID",
                        "name": "item_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "商品名称",
                        "name": "item_name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "每页显示的条目数量",
                        "name": "page_size",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "出售价格",
                        "name": "sale_price_gte",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "出售价格",
                        "name": "sale_price_lte",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sku_id",
                        "name": "sku_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "品牌",
                        "name": "trademark_ids",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "操作人",
                        "name": "updated_by",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/define.MallItemPageResp"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/v1/trade_order/cancel": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "取消订单",
                "tags": [
                    "管理端-订单管理"
                ],
                "summary": "取消订单",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.CancelAdminTradeOrderReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.CancelAdminTradeOrderResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/v1/trade_order/detail": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "管理端获取订单详细信息",
                "tags": [
                    "管理端-订单管理"
                ],
                "summary": "获取订单详情",
                "parameters": [
                    {
                        "type": "string",
                        "example": "0",
                        "name": "id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.GetAdminTradeOrderDetailResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/v1/trade_order/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "管理端获取订单分页列表",
                "tags": [
                    "管理端-订单管理"
                ],
                "summary": "获取订单列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "创建时间开始",
                        "name": "created_at_gte",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "创建时间结束",
                        "name": "created_at_lte",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "0",
                        "description": "订单号",
                        "name": "id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "商品ID",
                        "name": "item_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "商品名称",
                        "name": "item_name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "0",
                        "description": "直购商品ID",
                        "name": "mall_item_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）",
                        "name": "order_status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "每页显示的条目数量",
                        "name": "page_size",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "支付订单ID",
                        "name": "pay_order_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "商品 sku_id",
                        "name": "sku_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "user_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/define.GetAdminTradeOrderListResp"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/v1/trade_order/private_info": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "管理端获取订单隐私详情",
                "tags": [
                    "管理端-订单管理"
                ],
                "summary": "获取订单隐私详情",
                "parameters": [
                    {
                        "type": "string",
                        "example": "0",
                        "description": "订单id",
                        "name": "id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.AdminGetOrderPrivateInfoResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/v1/trade_order/refund": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "订单发起退款",
                "tags": [
                    "管理端-订单管理"
                ],
                "summary": "订单发起退款",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.TradeOrderRefundReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.TradeOrderRefundResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/v1/trade_order/sync_freight": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "同步订单物流信息",
                "tags": [
                    "管理端-订单管理"
                ],
                "summary": "同步订单物流信息",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.SyncAdminTradeOrderFreightReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.SyncAdminTradeOrderFreightResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/open/v1/mall_item/trigger_display": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "处理开始展示的直购商品",
                "tags": [
                    "open端-商品管理"
                ],
                "summary": "处理开始展示的直购商品",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.OpenTriggerDisplayByStartTimeReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/response.Data"
                        }
                    }
                }
            }
        },
        "/open/v1/mall_item/update_notify": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "商品信息变动通知",
                "tags": [
                    "open端-商品管理"
                ],
                "summary": "商品信息变动通知",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.OpenUpdateMallItemNotifyReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/response.Data"
                        }
                    }
                }
            }
        },
        "/open/v1/notice/supply_chain": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "供应链通知",
                "tags": [
                    "open端-通知管理"
                ],
                "summary": "供应链通知",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.SupplyChainNoticeReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.SupplyChainNoticeResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/open/v1/trade_order/detail": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取订单详情",
                "tags": [
                    "open端-订单管理"
                ],
                "summary": "获取订单详情",
                "parameters": [
                    {
                        "type": "string",
                        "example": "0",
                        "name": "id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "name": "user_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/define.GetTradeOrderDetailResp"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/open/v1/trade_order/finish": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "订单支付自动收货",
                "tags": [
                    "open端-订单管理"
                ],
                "summary": "订单支付自动收货",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.TradeOrderFinishReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.TradeOrderFinishResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/open/v1/trade_order/pay_success": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "订单支付成功回调",
                "tags": [
                    "open端-订单管理"
                ],
                "summary": "订单支付成功回调",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.TradeOrderPaySuccessReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.TradeOrderPaySuccessResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/open/v1/trade_order/retry_upload_to_supply": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "重新上传失败的订单到供应链",
                "tags": [
                    "open端-订单管理"
                ],
                "summary": "重新上传失败的订单到供应链",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.TradeOrderFailedRetryUploadToSupplyReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/response.Data"
                        }
                    }
                }
            }
        },
        "/open/v1/trade_order/timeout_close": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "订单支付超时关闭",
                "tags": [
                    "open端-订单管理"
                ],
                "summary": "订单支付超时关闭",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.TradeOrderTimeoutCloseReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/response.Data"
                        }
                    }
                }
            }
        },
        "/web/v1/mall_item/buy": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "下单购买",
                "tags": [
                    "用户端-直购商城"
                ],
                "summary": "下单购买",
                "parameters": [
                    {
                        "description": "获取参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.WebBuyReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.WebBuyResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/mall_item/detail": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取直购商品详情",
                "tags": [
                    "用户端-直购商城"
                ],
                "summary": "获取直购商品详情",
                "parameters": [
                    {
                        "type": "string",
                        "example": "0",
                        "description": "直购商品ID",
                        "name": "id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "商品ID",
                        "name": "item_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.WebGetMallItemDetailResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/mall_item/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取直购商品列表",
                "tags": [
                    "用户端-直购商城"
                ],
                "summary": "获取直购商品列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "商品 ip_id",
                        "name": "ip_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "排序类型 1=综合，2=销量，3=价格",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "每页显示的条目数量",
                        "name": "page_size",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "排序方式  asc-升序 desc-降序",
                        "name": "sort_order",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.WebGetMallItemListResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/trade_order/cancel": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "取消订单",
                "tags": [
                    "用户端-订单管理"
                ],
                "summary": "取消订单",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.CancelTradeOrderReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.CancelTradeOrderResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/trade_order/confirm": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "确定收货",
                "tags": [
                    "用户端-订单管理"
                ],
                "summary": "确定收货",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.ConfirmTradeOrderReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.ConfirmTradeOrderResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/trade_order/del": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "删除订单",
                "tags": [
                    "用户端-订单管理"
                ],
                "summary": "删除订单",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.DelTradeOrderReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.DelTradeOrderResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/trade_order/detail": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "查询直购订单详情",
                "tags": [
                    "用户端-订单管理"
                ],
                "summary": "查询直购订单详情",
                "parameters": [
                    {
                        "type": "string",
                        "example": "0",
                        "description": "订单id",
                        "name": "id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/define.GetWebTradeOrderDetailResp"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/trade_order/freight_info": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "查询直购订单物流信息",
                "tags": [
                    "用户端-订单管理"
                ],
                "summary": "查询直购订单物流信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "快递单号",
                        "name": "delivery_number",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "0",
                        "description": "订单id",
                        "name": "id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/define.GetWebTradeOrderFreightInfoResp"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/trade_order/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "查询直购订单列表",
                "tags": [
                    "用户端-订单管理"
                ],
                "summary": "查询直购订单列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）",
                        "name": "order_status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "每页显示的条目数量",
                        "name": "page_size",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.GetWebTradeOrderListResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/trade_order/status_stat": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "查询直购订单状态统计",
                "tags": [
                    "用户端-订单管理"
                ],
                "summary": "查询直购订单状态统计",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/define.GetWebTradeOrderStatusStatResp"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/trade_order/update_address": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "修改地址",
                "tags": [
                    "用户端-订单管理"
                ],
                "summary": "修改地址",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.UpdateTradeOrderAddressReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.UpdateTradeOrderAddressResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/user_wishlist/add": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "添加想要",
                "tags": [
                    "用户端-想要管理"
                ],
                "summary": "添加想要",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.WebWishlistReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/response.Data"
                        }
                    }
                }
            }
        },
        "/web/v1/user_wishlist/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "用户想要列表",
                "tags": [
                    "用户端-想要管理"
                ],
                "summary": "用户想要列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "每页显示的条目数量",
                        "name": "page_size",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Data"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/define.WebWishlistListResp"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/web/v1/user_wishlist/remove": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "移除想要",
                "tags": [
                    "用户端-想要管理"
                ],
                "summary": "移除想要",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/define.WebWishlistReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/response.Data"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "define.AddMallItemReq": {
            "type": "object",
            "required": [
                "daily_limit",
                "freight",
                "item_id",
                "sale_price",
                "sku_id",
                "start_time",
                "stock",
                "stock_type",
                "total_limit"
            ],
            "properties": {
                "daily_limit": {
                    "description": "每日限购数量",
                    "type": "integer",
                    "minimum": 0
                },
                "discount": {
                    "description": "折扣",
                    "type": "integer",
                    "maximum": 100,
                    "minimum": 1
                },
                "freight": {
                    "description": "运费(分)",
                    "type": "integer",
                    "minimum": 0
                },
                "item_id": {
                    "description": "商品ID",
                    "type": "string"
                },
                "priority": {
                    "description": "优先级",
                    "type": "integer",
                    "maximum": 9999,
                    "minimum": 0
                },
                "sale_price": {
                    "description": "售价(分)",
                    "type": "integer",
                    "minimum": 1
                },
                "sku_id": {
                    "description": "商品 sku_id",
                    "type": "string"
                },
                "start_time": {
                    "description": "开始展示时间",
                    "type": "string"
                },
                "stock": {
                    "description": "限定库存量",
                    "type": "integer",
                    "minimum": 1
                },
                "stock_type": {
                    "description": "库存类型（1=限定库存，2=同步商品库存）",
                    "type": "integer"
                },
                "total_limit": {
                    "description": "总限购数量",
                    "type": "integer",
                    "minimum": 0
                }
            }
        },
        "define.AddMallItemResp": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "直购商品ID",
                    "type": "string",
                    "example": "0"
                }
            }
        },
        "define.AdminGetOrderPrivateInfoResp": {
            "type": "object",
            "properties": {
                "consignee_phone": {
                    "description": "收货人电话",
                    "type": "string"
                }
            }
        },
        "define.CancelAdminTradeOrderReq": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "订单id",
                    "type": "string",
                    "example": "0"
                }
            }
        },
        "define.CancelAdminTradeOrderResp": {
            "type": "object"
        },
        "define.CancelTradeOrderReq": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "订单id",
                    "type": "string",
                    "example": "0"
                }
            }
        },
        "define.CancelTradeOrderResp": {
            "type": "object"
        },
        "define.ConfirmTradeOrderReq": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "订单id",
                    "type": "string",
                    "example": "0"
                }
            }
        },
        "define.ConfirmTradeOrderResp": {
            "type": "object"
        },
        "define.DelTradeOrderReq": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "订单id",
                    "type": "string",
                    "example": "0"
                }
            }
        },
        "define.DelTradeOrderResp": {
            "type": "object"
        },
        "define.EditMallItemPriorityReq": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "string",
                    "example": "0"
                },
                "priority": {
                    "description": "优先级",
                    "type": "integer",
                    "maximum": 9999,
                    "minimum": 1
                }
            }
        },
        "define.EditMallItemPriorityResp": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "直购商品ID",
                    "type": "string",
                    "example": "0"
                }
            }
        },
        "define.EditMallItemReq": {
            "type": "object",
            "required": [
                "daily_limit",
                "freight",
                "id",
                "item_id",
                "sale_price",
                "sku_id",
                "start_time",
                "stock",
                "stock_type",
                "total_limit"
            ],
            "properties": {
                "daily_limit": {
                    "description": "每日限购数量",
                    "type": "integer",
                    "minimum": 0
                },
                "discount": {
                    "description": "折扣",
                    "type": "integer",
                    "maximum": 100,
                    "minimum": 1
                },
                "freight": {
                    "description": "运费(分)",
                    "type": "integer",
                    "minimum": 0
                },
                "id": {
                    "description": "直购商品ID",
                    "type": "string",
                    "example": "0"
                },
                "item_id": {
                    "description": "商品ID",
                    "type": "string"
                },
                "priority": {
                    "description": "优先级",
                    "type": "integer",
                    "maximum": 9999,
                    "minimum": 0
                },
                "sale_price": {
                    "description": "售价(分)",
                    "type": "integer",
                    "minimum": 1
                },
                "sku_id": {
                    "description": "商品 sku_id",
                    "type": "string"
                },
                "start_time": {
                    "description": "开始展示时间",
                    "type": "string"
                },
                "stock": {
                    "description": "限定库存量",
                    "type": "integer",
                    "minimum": 1
                },
                "stock_type": {
                    "description": "库存类型（1=限定库存，2=同步商品库存）",
                    "type": "integer"
                },
                "total_limit": {
                    "description": "总限购数量",
                    "type": "integer",
                    "minimum": 0
                }
            }
        },
        "define.EditMallItemResp": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "直购商品ID",
                    "type": "string",
                    "example": "0"
                }
            }
        },
        "define.EditMallItemStatusReq": {
            "type": "object",
            "required": [
                "id",
                "status"
            ],
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "string",
                    "example": "0"
                },
                "status": {
                    "description": "状态（0=待上架，1=已上架，2=已下架）",
                    "type": "integer",
                    "minimum": 0
                }
            }
        },
        "define.EditMallItemStatusResp": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "直购商品ID",
                    "type": "string",
                    "example": "0"
                }
            }
        },
        "define.GetAdminTradeOrderData": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "freight_amount": {
                    "description": "运费",
                    "type": "integer"
                },
                "id": {
                    "description": "订单ID",
                    "type": "string",
                    "example": "0"
                },
                "order_items": {
                    "description": "订单子单列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.GetAdminTradeOrderItemData"
                    }
                },
                "order_status": {
                    "description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）",
                    "type": "integer"
                },
                "pay_amount": {
                    "description": "支付金额",
                    "type": "integer"
                },
                "total_amount": {
                    "description": "总金额",
                    "type": "integer"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "string"
                },
                "user_nickname": {
                    "description": "用户昵称",
                    "type": "string"
                }
            }
        },
        "define.GetAdminTradeOrderDetailFreight": {
            "type": "object",
            "properties": {
                "delivered_at": {
                    "description": "发货时间",
                    "type": "string"
                },
                "delivery_company": {
                    "description": "快递公司",
                    "type": "string"
                },
                "delivery_number": {
                    "description": "快递单号",
                    "type": "string"
                },
                "order_item_id": {
                    "description": "订单子单ID",
                    "type": "string",
                    "example": "0"
                },
                "records": {
                    "description": "物流信息",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "define.GetAdminTradeOrderDetailResp": {
            "type": "object",
            "properties": {
                "cancel_at": {
                    "description": "取消时间",
                    "type": "string"
                },
                "cancel_type": {
                    "description": "取消类型（1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）",
                    "type": "integer"
                },
                "consignee_address": {
                    "description": "收货地址",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "consignee_name": {
                    "description": "收货人姓名",
                    "type": "string"
                },
                "consignee_phone": {
                    "description": "收货人电话",
                    "type": "string"
                },
                "created_at": {
                    "description": "下单时间",
                    "type": "string"
                },
                "delivered_at": {
                    "description": "发货时间",
                    "type": "string"
                },
                "discount_amount": {
                    "description": "折扣金额",
                    "type": "integer"
                },
                "finished_at": {
                    "description": "完成时间",
                    "type": "string"
                },
                "freight_amount": {
                    "description": "运费",
                    "type": "integer"
                },
                "freight_infos": {
                    "description": "物流信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.GetAdminTradeOrderDetailFreight"
                    }
                },
                "id": {
                    "description": "订单ID",
                    "type": "string",
                    "example": "0"
                },
                "order_items": {
                    "description": "物品列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.GetAdminTradeOrderItemData"
                    }
                },
                "order_status": {
                    "description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）",
                    "type": "integer"
                },
                "order_type": {
                    "description": "订单类型（1=直购订单）",
                    "type": "integer"
                },
                "pay_amount": {
                    "description": "支付金额",
                    "type": "integer"
                },
                "pay_order_id": {
                    "description": "支付订单ID",
                    "type": "string"
                },
                "payment_method": {
                    "description": "支付方式",
                    "type": "string"
                },
                "payment_status": {
                    "description": "支付状态（0=未支付，1=已支付，2=退款，3=全额退款）",
                    "type": "integer"
                },
                "payment_time": {
                    "description": "支付时间",
                    "type": "string"
                },
                "shipping_status": {
                    "description": "发货状态（0=未发货，1=已发货，2=已签收）",
                    "type": "integer"
                },
                "supply_chain_order_id": {
                    "description": "供应链ID",
                    "type": "string",
                    "example": ""
                },
                "terminal": {
                    "description": "订单渠道",
                    "type": "string"
                },
                "total_amount": {
                    "description": "总金额",
                    "type": "integer"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "string"
                },
                "user_nickname": {
                    "description": "用户昵称",
                    "type": "string"
                },
                "user_remark": {
                    "description": "用户备注",
                    "type": "string"
                }
            }
        },
        "define.GetAdminTradeOrderItemData": {
            "type": "object",
            "properties": {
                "brand_name": {
                    "description": "商品品牌名称",
                    "type": "string"
                },
                "category_id": {
                    "description": "商品分类id",
                    "type": "string"
                },
                "category_name": {
                    "description": "商品分类名称",
                    "type": "string"
                },
                "discount_amount": {
                    "description": "商品享受的优惠金额",
                    "type": "integer"
                },
                "freight_amount": {
                    "description": "运费",
                    "type": "integer"
                },
                "id": {
                    "description": "直购商品ID",
                    "type": "string",
                    "example": "0"
                },
                "ip_id": {
                    "description": "商品 ip_id",
                    "type": "string"
                },
                "ip_name": {
                    "description": "商品IP名称",
                    "type": "string"
                },
                "item_icon_url": {
                    "description": "商品主图",
                    "type": "string"
                },
                "item_id": {
                    "description": "商品ID",
                    "type": "string"
                },
                "item_name": {
                    "description": "商品名称",
                    "type": "string"
                },
                "item_specs": {
                    "description": "商品属性",
                    "type": "string"
                },
                "quantity": {
                    "description": "购买数量",
                    "type": "integer"
                },
                "sale_price": {
                    "description": "单价",
                    "type": "integer"
                },
                "sku_id": {
                    "description": "商品 sku_id",
                    "type": "string"
                },
                "trademark_id": {
                    "description": "商品品牌id",
                    "type": "string"
                }
            }
        },
        "define.GetAdminTradeOrderListResp": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.GetAdminTradeOrderData"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "define.GetTradeOrderDetailResp": {
            "type": "object",
            "properties": {
                "pay_amount": {
                    "description": "支付金额(单位:分)",
                    "type": "integer"
                },
                "payment_status": {
                    "description": "支付状态（0=未支付，1=已支付，2=退款，3=全额退款）",
                    "type": "integer"
                }
            }
        },
        "define.GetWebTradeOrderData": {
            "type": "object",
            "properties": {
                "countdown_second": {
                    "description": "倒计时秒数",
                    "type": "integer"
                },
                "freight_info": {
                    "description": "物流信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.GetWebTradeOrderDetailFreight"
                    }
                },
                "id": {
                    "description": "id",
                    "type": "string",
                    "example": "0"
                },
                "order_items": {
                    "description": "物品列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.GetWebTradeOrderItemData"
                    }
                },
                "order_status": {
                    "description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）",
                    "type": "integer"
                },
                "pay_amount": {
                    "description": "支付金额",
                    "type": "integer"
                },
                "payment_status": {
                    "description": "支付状态（0=未支付，1=已支付，2=退款，3=全额退款）",
                    "type": "integer"
                },
                "shipping_status": {
                    "description": "发货状态（0=未发货，1=已发货，2=已签收）",
                    "type": "integer"
                },
                "total_amount": {
                    "description": "总金额",
                    "type": "integer"
                }
            }
        },
        "define.GetWebTradeOrderDetailFreight": {
            "type": "object",
            "properties": {
                "delivery_company": {
                    "description": "快递公司",
                    "type": "string"
                },
                "delivery_number": {
                    "description": "快递单号",
                    "type": "string"
                }
            }
        },
        "define.GetWebTradeOrderDetailResp": {
            "type": "object",
            "properties": {
                "cancel_type": {
                    "description": "取消类型（1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）",
                    "type": "integer"
                },
                "consignee_address": {
                    "description": "收货地址",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "consignee_name": {
                    "description": "收货人姓名",
                    "type": "string"
                },
                "consignee_phone": {
                    "description": "收货人电话",
                    "type": "string"
                },
                "countdown_second": {
                    "description": "倒计时秒数",
                    "type": "integer"
                },
                "created_at": {
                    "description": "下单时间",
                    "type": "string"
                },
                "discount_amount": {
                    "description": "折扣金额",
                    "type": "integer"
                },
                "finished_second": {
                    "description": "订单完成倒计时秒数",
                    "type": "integer"
                },
                "freight_amount": {
                    "description": "运费",
                    "type": "integer"
                },
                "freight_info": {
                    "description": "物流信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.GetWebTradeOrderDetailFreight"
                    }
                },
                "id": {
                    "description": "id",
                    "type": "string",
                    "example": "0"
                },
                "order_items": {
                    "description": "物品列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.GetWebTradeOrderItemData"
                    }
                },
                "order_status": {
                    "description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）",
                    "type": "integer"
                },
                "pay_amount": {
                    "description": "支付金额",
                    "type": "integer"
                },
                "payment_method": {
                    "description": "支付方式",
                    "type": "string"
                },
                "payment_status": {
                    "description": "支付状态（0=未支付，1=已支付，2=退款，3=全额退款）",
                    "type": "integer"
                },
                "payment_time": {
                    "description": "支付时间",
                    "type": "string"
                },
                "shipping_status": {
                    "description": "发货状态（0=未发货，1=已发货，2=已签收）",
                    "type": "integer"
                },
                "total_amount": {
                    "description": "总金额",
                    "type": "integer"
                },
                "user_remark": {
                    "description": "用户备注",
                    "type": "string"
                }
            }
        },
        "define.GetWebTradeOrderFreightInfoResp": {
            "type": "object",
            "properties": {
                "checked": {
                    "description": "0未签收 1已签收",
                    "type": "integer"
                },
                "delivery_company": {
                    "description": "快递公司",
                    "type": "string"
                },
                "delivery_number": {
                    "description": "快递单号",
                    "type": "string"
                },
                "records": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "status": {
                    "type": "integer"
                }
            }
        },
        "define.GetWebTradeOrderItemData": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "直购商品ID",
                    "type": "string",
                    "example": "0"
                },
                "item_icon_url": {
                    "description": "商品主图",
                    "type": "string"
                },
                "item_id": {
                    "description": "商品ID",
                    "type": "string"
                },
                "item_name": {
                    "description": "商品名称",
                    "type": "string"
                },
                "item_specs": {
                    "description": "商品属性",
                    "type": "string"
                },
                "quantity": {
                    "description": "购买数量",
                    "type": "integer"
                },
                "sale_price": {
                    "description": "单价",
                    "type": "integer"
                }
            }
        },
        "define.GetWebTradeOrderListResp": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.GetWebTradeOrderData"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "define.GetWebTradeOrderStatusStatData": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "order_status": {
                    "description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）",
                    "type": "integer"
                }
            }
        },
        "define.GetWebTradeOrderStatusStatResp": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.GetWebTradeOrderStatusStatData"
                    }
                }
            }
        },
        "define.MallItemDetailResp": {
            "type": "object",
            "properties": {
                "available_stock": {
                    "description": "直购商品可用库存",
                    "type": "integer"
                },
                "category_id": {
                    "description": "商品分类id",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "daily_limit": {
                    "description": "每日限购数量",
                    "type": "integer"
                },
                "discount": {
                    "description": "折扣",
                    "type": "integer"
                },
                "freight": {
                    "description": "运费(分)",
                    "type": "integer"
                },
                "id": {
                    "description": "主键",
                    "type": "string",
                    "example": "0"
                },
                "ipid": {
                    "description": "商品 ip_id",
                    "type": "string"
                },
                "item_id": {
                    "description": "商品 id",
                    "type": "string"
                },
                "item_name": {
                    "description": "商品名称",
                    "type": "string"
                },
                "item_specs": {
                    "description": "商品规格",
                    "type": "string"
                },
                "priority": {
                    "description": "优先级",
                    "type": "integer"
                },
                "purchase_price": {
                    "description": "进价",
                    "type": "integer"
                },
                "sale_price": {
                    "description": "售价(分)",
                    "type": "integer"
                },
                "sell_listings": {
                    "description": "商品库存",
                    "type": "integer"
                },
                "sku_id": {
                    "description": "商品 sku_id",
                    "type": "string"
                },
                "spu_id": {
                    "description": "商品 spu_id",
                    "type": "string"
                },
                "start_time": {
                    "description": "开始展示时间",
                    "type": "string"
                },
                "status": {
                    "description": "状态（0=待上架，1=已上架，2=已下架）",
                    "type": "integer"
                },
                "stock": {
                    "description": "限定库存量",
                    "type": "integer"
                },
                "stock_type": {
                    "description": "库存类型（1=限定库存，2=同步商品库存）",
                    "type": "integer"
                },
                "total_limit": {
                    "description": "总限购数量",
                    "type": "integer"
                },
                "trademark_id": {
                    "description": "商品品牌id",
                    "type": "string"
                }
            }
        },
        "define.MallItemPageData": {
            "type": "object",
            "properties": {
                "available_stock": {
                    "description": "剩余库存",
                    "type": "integer"
                },
                "brand_name": {
                    "description": "商品品牌名称",
                    "type": "string"
                },
                "category_id": {
                    "description": "商品分类ID",
                    "type": "string"
                },
                "category_name": {
                    "description": "商品分类名称",
                    "type": "string"
                },
                "discount": {
                    "description": "折扣",
                    "type": "integer"
                },
                "discount_price": {
                    "description": "折扣价",
                    "type": "integer"
                },
                "freight": {
                    "description": "运费",
                    "type": "integer"
                },
                "id": {
                    "description": "主键",
                    "type": "string",
                    "example": "0"
                },
                "ip_id": {
                    "description": "IP ID",
                    "type": "string"
                },
                "ip_name": {
                    "description": "IP名称",
                    "type": "string"
                },
                "item_icon_url": {
                    "description": "商品主图",
                    "type": "string"
                },
                "item_id": {
                    "description": "商品 ID",
                    "type": "string"
                },
                "item_name": {
                    "description": "商品名称",
                    "type": "string"
                },
                "item_specs": {
                    "description": "规格",
                    "type": "string"
                },
                "priority": {
                    "description": "优先级",
                    "type": "integer"
                },
                "purchase_price": {
                    "description": "进价",
                    "type": "integer"
                },
                "sale_price": {
                    "description": "售价",
                    "type": "integer"
                },
                "sku_id": {
                    "description": "SKU ID",
                    "type": "string"
                },
                "spu_id": {
                    "description": "SPU ID",
                    "type": "string"
                },
                "start_time": {
                    "description": "开始展示时间",
                    "type": "string"
                },
                "status": {
                    "description": "状态（0=待上架，1=已上架，2=已下架）",
                    "type": "integer"
                },
                "stock": {
                    "description": "销售库存",
                    "type": "integer"
                },
                "trademark_id": {
                    "description": "商品品牌ID",
                    "type": "string"
                },
                "updated_by": {
                    "description": "最后操作人",
                    "type": "string"
                }
            }
        },
        "define.MallItemPageResp": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.MallItemPageData"
                    }
                },
                "total": {
                    "description": "总数",
                    "type": "integer"
                }
            }
        },
        "define.OpenTriggerDisplayByStartTimeReq": {
            "type": "object"
        },
        "define.OpenUpdateMallItemNotifyReq": {
            "type": "object",
            "required": [
                "item_ids"
            ],
            "properties": {
                "item_ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "define.SupplyChainNoticeReq": {
            "type": "object",
            "required": [
                "type"
            ],
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "additionalProperties": true
                    }
                },
                "type": {
                    "type": "integer"
                }
            }
        },
        "define.SupplyChainNoticeResp": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "object",
                    "properties": {
                        "failed": {
                            "description": "失败的消息ID（请求参数中的\"mid\"）",
                            "type": "array",
                            "items": {
                                "type": "integer"
                            }
                        }
                    }
                },
                "desc": {
                    "type": "string"
                }
            }
        },
        "define.SyncAdminTradeOrderFreightReq": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "订单详情ID",
                    "type": "string",
                    "example": "0"
                }
            }
        },
        "define.SyncAdminTradeOrderFreightResp": {
            "type": "object"
        },
        "define.TradeOrderFailedRetryUploadToSupplyReq": {
            "type": "object"
        },
        "define.TradeOrderFinishReq": {
            "type": "object"
        },
        "define.TradeOrderFinishResp": {
            "type": "object"
        },
        "define.TradeOrderPaySuccessReq": {
            "type": "object",
            "required": [
                "id",
                "recharge_order_id"
            ],
            "properties": {
                "id": {
                    "type": "string",
                    "example": "0"
                },
                "pay_method": {
                    "type": "string"
                },
                "pay_time": {
                    "type": "string"
                },
                "recharge_order_id": {
                    "type": "string"
                }
            }
        },
        "define.TradeOrderPaySuccessResp": {
            "type": "object"
        },
        "define.TradeOrderRefundReq": {
            "type": "object",
            "required": [
                "amount",
                "recharge_order_id"
            ],
            "properties": {
                "amount": {
                    "type": "integer"
                },
                "recharge_order_id": {
                    "type": "string"
                }
            }
        },
        "define.TradeOrderRefundResp": {
            "type": "object",
            "properties": {
                "amount": {
                    "type": "integer"
                }
            }
        },
        "define.TradeOrderTimeoutCloseReq": {
            "type": "object"
        },
        "define.UpdateTradeOrderAddressReq": {
            "type": "object",
            "required": [
                "address_id",
                "id"
            ],
            "properties": {
                "address_id": {
                    "description": "地址ID",
                    "type": "string"
                },
                "id": {
                    "description": "订单id",
                    "type": "string",
                    "example": "0"
                }
            }
        },
        "define.UpdateTradeOrderAddressResp": {
            "type": "object"
        },
        "define.WebBuyReq": {
            "type": "object",
            "required": [
                "address_id",
                "mall_item_id",
                "pay_amount",
                "quantity"
            ],
            "properties": {
                "address_id": {
                    "description": "地址ID",
                    "type": "string"
                },
                "mall_item_id": {
                    "description": "直购商品ID",
                    "type": "string",
                    "example": "0"
                },
                "pay_amount": {
                    "description": "支付金额",
                    "type": "integer"
                },
                "quantity": {
                    "description": "购买数量",
                    "type": "integer"
                },
                "remark": {
                    "description": "备注",
                    "type": "string"
                }
            }
        },
        "define.WebBuyResp": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "is_new_order": {
                    "description": "是否是新订单",
                    "type": "boolean"
                },
                "order_id": {
                    "description": "直购订单ID",
                    "type": "string",
                    "example": "0"
                },
                "pay_amount": {
                    "description": "应支付金额",
                    "type": "integer"
                }
            }
        },
        "define.WebGetMallItemDetailResp": {
            "type": "object",
            "properties": {
                "available_stock": {
                    "description": "剩余库存",
                    "type": "integer"
                },
                "brand_name": {
                    "description": "商品品牌名称",
                    "type": "string"
                },
                "category_id": {
                    "description": "商品分类id",
                    "type": "string"
                },
                "category_name": {
                    "description": "商品分类名称",
                    "type": "string"
                },
                "detail": {
                    "description": "商品详情",
                    "type": "string"
                },
                "discount": {
                    "description": "折扣",
                    "type": "integer"
                },
                "discount_price": {
                    "description": "折扣价",
                    "type": "integer"
                },
                "freight": {
                    "description": "运费",
                    "type": "integer"
                },
                "id": {
                    "description": "直购商品ID",
                    "type": "string",
                    "example": "0"
                },
                "image_infos": {
                    "description": "商品图片",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "ip_id": {
                    "description": "商品 ip_id",
                    "type": "string"
                },
                "ip_name": {
                    "description": "商品IP名称",
                    "type": "string"
                },
                "item_icon_url": {
                    "description": "商品主图",
                    "type": "string"
                },
                "item_id": {
                    "description": "商品ID",
                    "type": "string"
                },
                "item_name": {
                    "description": "商品名称",
                    "type": "string"
                },
                "sale_price": {
                    "description": "售价",
                    "type": "integer"
                },
                "specs": {
                    "description": "商品规格",
                    "type": "string"
                },
                "status": {
                    "description": "直购商品状态（0=待上架，1=已上架，2=已下架）",
                    "type": "integer"
                },
                "stock": {
                    "description": "总库存",
                    "type": "integer"
                },
                "trademark_id": {
                    "description": "商品品牌id",
                    "type": "string"
                },
                "user_wishlist_status": {
                    "description": "用户心愿单状态",
                    "type": "integer"
                }
            }
        },
        "define.WebGetMallItemListResp": {
            "type": "object",
            "properties": {
                "has_more": {
                    "description": "判断当前页是否为最后一页",
                    "type": "boolean"
                },
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.WebMallItemListData"
                    }
                }
            }
        },
        "define.WebMallItemListData": {
            "type": "object",
            "properties": {
                "discount": {
                    "description": "折扣",
                    "type": "integer"
                },
                "discount_price": {
                    "description": "折扣价",
                    "type": "integer"
                },
                "id": {
                    "description": "直购商品ID",
                    "type": "string",
                    "example": "0"
                },
                "item_icon_url": {
                    "description": "商品主图",
                    "type": "string"
                },
                "item_id": {
                    "description": "商品ID",
                    "type": "string"
                },
                "item_name": {
                    "description": "商品名称",
                    "type": "string"
                },
                "priority": {
                    "description": "优先级",
                    "type": "integer"
                },
                "sale_price": {
                    "description": "售价",
                    "type": "integer"
                },
                "sales": {
                    "description": "销量",
                    "type": "integer"
                },
                "wishlist_count": {
                    "description": "想要数量",
                    "type": "integer"
                }
            }
        },
        "define.WebWishlistListData": {
            "type": "object",
            "properties": {
                "discount": {
                    "description": "折扣",
                    "type": "integer"
                },
                "discount_price": {
                    "description": "折扣价",
                    "type": "integer"
                },
                "id": {
                    "description": "用户想要ID",
                    "type": "string",
                    "example": "0"
                },
                "item_icon_url": {
                    "description": "商品主图",
                    "type": "string"
                },
                "item_id": {
                    "description": "商品ID",
                    "type": "string"
                },
                "item_name": {
                    "description": "商品名称",
                    "type": "string"
                },
                "mall_item_id": {
                    "description": "直购商品ID",
                    "type": "integer"
                },
                "sale_price": {
                    "description": "售价",
                    "type": "integer"
                }
            }
        },
        "define.WebWishlistListResp": {
            "type": "object",
            "properties": {
                "has_more": {
                    "description": "判断当前页是否为最后一页",
                    "type": "boolean"
                },
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/define.WebWishlistListData"
                    }
                }
            }
        },
        "define.WebWishlistReq": {
            "type": "object",
            "required": [
                "item_id"
            ],
            "properties": {
                "item_id": {
                    "type": "string"
                }
            }
        },
        "response.Data": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "desc": {
                    "type": "string"
                },
                "trace_id": {
                    "description": "链路id",
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "Bearer": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfomarketplace_service holds exported Swagger Info so clients can modify it
var SwaggerInfomarketplace_service = &swag.Spec{
	Version:          "1.0.0",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "app API",
	Description:      "接口文档",
	InfoInstanceName: "marketplace_service",
	SwaggerTemplate:  docTemplatemarketplace_service,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfomarketplace_service.InstanceName(), SwaggerInfomarketplace_service)
}
