package yc_open

import (
	"context"
	"crypto/md5"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"encoding/hex"
	"fmt"
	"marketplace_service/global"
	"sort"
	"strings"
)

type GetAppAccessResp struct {
	Code int32            `json:"code" form:"code"`
	Desc string           `json:"desc" form:"desc"`
	Data *GetAppAccessRes `json:"data" form:"data"`
}

type GetAppAccessRes struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

// GetAppAccess 获取应用token
func GetAppAccess(ctx context.Context) (*GetAppAccessRes, error) {
	rsp := &GetAppAccessResp{}
	url := "/open/session/app_access_token"
	err := utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url),
		utilRequest.WithParams(map[string]interface{}{
			"client_id":     global.GlobalConfig.Yc.ClientId,
			"client_secret": global.GlobalConfig.Yc.ClientSecret,
		}),
		utilRequest.WithMethodGet()).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("获取应用token异常，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("获取应用token异常，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("获取应用token失败")
	}
	return rsp.Data, err
}

// MD5Sign MD5生成签名
func MD5Sign(jsonObject map[string]interface{}) string {
	sortedObj := ObjKeySort(jsonObject)
	urlStr := ObjectToURL(jsonObject, sortedObj, nil)
	hasher := md5.New()
	hasher.Write([]byte(urlStr))
	return hex.EncodeToString(hasher.Sum(nil))
}

// MD5SignConvertObj MD5生成签名
func MD5SignConvertObj(jsonObject map[string]interface{}, convertObjKeyMap map[string]interface{}) string {
	sortedObj := ObjKeySort(jsonObject)
	urlStr := ObjectToURL(jsonObject, sortedObj, convertObjKeyMap)
	hasher := md5.New()
	hasher.Write([]byte(urlStr))
	return hex.EncodeToString(hasher.Sum(nil))
}

// ObjectToURL 生成URL参数字符串
func ObjectToURL(data map[string]interface{}, sortKey []string, convertObjKeyMap map[string]interface{}) string {
	var parts []string

	for _, key := range sortKey {
		value := data[key]
		strValue := formatValue(value, key, convertObjKeyMap)
		parts = append(parts, fmt.Sprintf("%s=%s", key, strValue))
	}

	urlStr := strings.Join(parts, "&")
	return urlStr
}

// formatValue 格式化值为字符串
func formatValue(value interface{}, key string, convertObjKeyMap map[string]interface{}) string {
	if convertObjKeyMap != nil {
		if v, ok := convertObjKeyMap[key]; ok {
			if slice, ok := v.([]int32); ok {
				strSlice := make([]string, len(slice))
				for i := range slice {
					strSlice[i] = "[object Object]"
				}
				return strings.Join(strSlice, ",")
			} else {
				return "[object Object]"
			}
		}
	}

	switch v := value.(type) {
	case string:
		return v
	case []string:
		return strings.Join(v, ",")
	case []interface{}:
		var strSlice []string
		for _, item := range v {
			strSlice = append(strSlice, fmt.Sprintf("%v", item))
		}
		return strings.Join(strSlice, ",")
	default:
		if key != "queryStr" {
			return fmt.Sprintf("%v", v)
		}
		return "[object Object]"
	}
}

// ObjKeySort 对对象键进行排序并过滤不需要的字段
func ObjKeySort(obj map[string]interface{}) []string {
	signBody := make(map[string]interface{})

	// 过滤掉不需要的字段
	for key, value := range obj {
		if key != "login_code" && key != "sign" {
			signBody[key] = value
		}
	}

	// 获取排序后的键
	keys := make([]string, 0, len(signBody))
	for k := range signBody {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	return keys
}
