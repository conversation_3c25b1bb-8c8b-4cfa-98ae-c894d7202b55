package yc_open

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"marketplace_service/global"
)

type QuerySteamItemResp struct {
	Code int32               `json:"code" form:"code"`
	Desc string              `json:"desc" form:"desc"`
	Data *QuerySteamItemData `json:"data" form:"data"`
}

type QuerySteamItemData struct {
	SkuNo         string `json:"sku_no"`
	ItemID        string `json:"item_id"`
	ItemName      string `json:"item_name"`
	Status        int32  `json:"status"`
	SupplyStatus  int32  `json:"supply_status"`
	ProductId     string `json:"product_id"`
	ProductNo     string `json:"product_no"`
	PurchasePrice int64  `json:"purchase_price"`
	SellPrice     int64  `json:"sell_price"`
	SellListings  int32  `json:"sell_listings"`
}

// QuerySteamItemBySkuNo 查询Yc商品信息
func QuerySteamItemBySkuNo(ctx context.Context, skuNo string) (*QuerySteamItemData, error) {
	rsp := &QuerySteamItemResp{}

	params := map[string]interface{}{
		"sku_no": skuNo,
		"sign":   "",
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("querySteamItemBySkuNo sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/steam_item/query_steam_item_by_sku_no"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询商品信息，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询商品信息，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("查询商品信息失败")
	}
	return rsp.Data, err
}
