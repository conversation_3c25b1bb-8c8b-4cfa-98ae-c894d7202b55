package define

type WithdrawOrderAddressInfo struct {
	Name        string `json:"name"`
	MobilePhone string `json:"mobile_phone"`
	Code        string `json:"code"`
	Area        string `json:"area"`
	Place       string `json:"place"`
}

type (
	BonusExchangeWithdrawOrderReq struct {
		OpenUserId    string                    `json:"open_user_id"`
		ExchangeLogId string                    `json:"exchange_log_id"`
		ExchangeQty   int32                     `json:"exchange_qty"`
		AddressId     string                    `json:"address_id"`
		AddressInfo   *WithdrawOrderAddressInfo `json:"address_info"`
		SteamItemId   string                    `json:"steam_item_id"`
	}
	BonusExchangeWithdrawOrderInfo struct {
		ItemWithdrawOrderID string `json:"item_withdraw_order_id"`
	}
	BonusExchangeWithdrawOrderResp struct {
		Code int32                          `json:"code"`
		Desc string                         `json:"desc"`
		Data BonusExchangeWithdrawOrderInfo `json:"data"`
	}
)

type (
	Wc<PERSON>allWithdrawOrderReq struct {
		OpenUserId    string                    `json:"open_user_id"`
		TotalAmount   int64                     `json:"total_amount"`
		PayAmount     int64                     `json:"pay_amount"`
		FreightAmount int64                     `json:"freight_amount"`
		WcOrderId     string                    `json:"wc_order_id"`
		BuyQuantity   int32                     `json:"buy_quantity"`
		BuyPrice      int64                     `json:"buy_price"`
		AddressId     string                    `json:"address_id"`
		AddressInfo   *WithdrawOrderAddressInfo `json:"address_info"`
		ItemId        string                    `json:"item_id"`
		Remark        string                    `json:"remark"`
	}
	WcMallWithdrawOrderInfo struct {
		ItemWithdrawOrderID string `json:"item_withdraw_order_id"`
	}
	WcMallWithdrawOrderResp struct {
		Code int32                   `json:"code"`
		Desc string                  `json:"desc"`
		Data WcMallWithdrawOrderInfo `json:"data"`
	}
)
