package wat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type GetRechargeInfoResp struct {
	Code int32                `json:"code" form:"code"`
	Desc string               `json:"desc" form:"desc"`
	Data *GetRechargeInfoData `json:"data" form:"data"`
}

type GetRechargeInfoData struct {
	ID              int64  `form:"id,string" json:"id,string"`
	RechargeOrderID string `form:"recharge_order_id" json:"recharge_order_id"`
	PayMethod       string `form:"pay_method" json:"pay_method"`
	PayTime         string `form:"pay_time" json:"pay_time"`
}

func GetRechargeInfo(ctx context.Context, id, payOrderId string) (*GetRechargeInfoData, error) {
	rsp := &GetRechargeInfoResp{}
	req, err := request.Wat()
	if err != nil {
		return nil, err
	}
	params := make(map[string]interface{}, 0)
	if id != "" {
		params["mall_order_id"] = id
	} else if payOrderId != "" {
		params["order_id"] = payOrderId
	}
	err = req.Call(
		ctx,
		"open/wallet/order/v1/recharge/info",
		&rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodGet(),
	)
	if err != nil {
		return nil, err
	}

	if rsp.Code != 0 {
		return nil, response.Fail.SetMsg(rsp.Desc)
	}
	return rsp.Data, err
}
