package wat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type GetPayStatusResp struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data bool   `json:"data" form:"data"`
}

func GetPayStatus(ctx context.Context, order string) (bool, error) {
	rsp := &GetPayStatusResp{}
	req, err := request.Wat()
	if err != nil {
		return false, err
	}
	err = req.Call(
		ctx,
		"open/wallet/order/v1/recharge/pay_status",
		&rsp,
		utilRequest.WithParams(map[string]interface{}{"order": order}),
		utilRequest.WithMethodGet(),
	)
	if err != nil {
		return false, err
	}

	if rsp.Code != 0 {
		return false, response.Fail.SetMsg(rsp.Desc)
	}
	return rsp.Data, err
}
