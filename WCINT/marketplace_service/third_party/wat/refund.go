package wat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"marketplace_service/pkg/utils"
	"net"
	"time"
)

type RefundForm struct {
	Amount          int64  `json:"amount" form:"amount"`                       // 退款金额
	RechargeOrderId string `json:"recharge_order_id" form:"recharge_order_id"` // 充值订单订单id
}

type RefundRsp struct {
	Code int32      `json:"code" form:"code"`
	Desc string     `json:"desc" form:"desc"`
	Data RefundData `json:"data" form:"data"`
}

type RefundData struct {
	IsSuccess    bool  `json:"is_success" form:"is_success"`
	RefundAmount int64 `json:"refund_amount" form:"refund_amount"` // 退款金额
}

type RefundRes struct {
	Status       int32 `json:"status"`
	RefundAmount int64 `json:"refund_amount" form:"refund_amount"` // 退款金额
}

const (
	RefundSuccess = 1
	RefundUnknown = 0
	RefundFail    = -1
)

// Refund 订单发起退款
func Refund(ctx context.Context, form *RefundForm) (*RefundRes, error) {
	rsp := &RefundRsp{}
	res := &RefundRes{}
	req, err := request.Wat()
	if err != nil {
		res.Status = RefundFail
		return res, err
	}
	params := map[string]interface{}{
		"recharge_order_id": form.RechargeOrderId,
		"amount":            form.Amount,
	}

	err = req.Call(
		ctx,
		"open/wallet/order/v1/refund_order/refund",
		&rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(30*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			res.Status = RefundUnknown
			return res, response.Fail.SetMsg("订单发起退款请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("订单发起退款超时异常，返回数据：%v", err)
			res.Status = RefundUnknown
			return res, response.Fail.SetMsg("订单发起退款请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("订单发起退款异常，返回数据：%v", err)
		res.Status = RefundFail
		return res, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("订单发起退款异常，返回数据：%v", utils.Obj2JsonStr(rsp))
		res.Status = RefundFail
		return res, response.Fail.SetMsg("订单发起退款失败")
	}
	log.Ctx(ctx).Infof("订单发起退款成功，返回数据：%v", utils.Obj2JsonStr(rsp))
	res.Status = RefundSuccess
	res.RefundAmount = rsp.Data.RefundAmount
	return res, err
}
