package wae

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/pkg/errors"
)

type FreightQueryRequest struct {
	FreightNo string `json:"freight_no"` // 快递单号
}

// FreightQueryResponse 定义接口返回结构
type FreightQueryResponse struct {
	Code    int                       `json:"code"`
	Message string                    `json:"message"`
	Data    *FreightQueryResponseData `json:"data"`
}

type FreightQueryResponseData struct {
	FreightNo string `json:"freight_no"`
	Status    int    `json:"status"`
	Checked   int    `json:"checked"`
	Com       string `json:"com"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
	Info      []struct {
		Time     string  `json:"time"`
		Context  string  `json:"context"`
		Ftime    string  `json:"ftime"`
		AreaCode *string `json:"areaCode"`
		AreaName *string `json:"areaName"`
		Status   string  `json:"status"`
	} `json:"info"`
}

// FreightQuery 订单修改收货信息
func FreightQuery(ctx context.Context, request *FreightQueryRequest) (*FreightQueryResponseData, error) {
	var rsp FreightQueryResponse
	if err := CommonRequest(
		ctx,
		"/open/v1/freight/detail/query",
		request,
		&rsp,
	); err != nil {
		log.Ctx(ctx).Errorf("FreightQuery 订单修改收货信息失败 err: %v", err)
		return nil, err
	}
	if rsp.Code == 1000 {
		return rsp.Data, nil
	}
	return nil, errors.New(rsp.Message)
}
