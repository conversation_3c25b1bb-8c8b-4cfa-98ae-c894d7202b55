package tmt

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type SteamItemInfo struct {
	Id               string              `json:"_id" form:"id"`
	SkuId            string              `json:"sku_no" form:"sku_no"`
	ItemName         string              `json:"item_name" form:"item_name"`                   // 商品名称
	IconUrl          string              `json:"icon_url" form:"icon_url"`                     // 商品图片
	PurchasePrice    int64               `json:"purchase_price" form:"purchase_price"`         // 商品进价
	SellPrice        int64               `json:"sell_price" form:"sell_price"`                 // 商品售价
	SellListings     int32               `json:"sell_listings" form:"sell_listings"`           // 商品库存
	Specs            string              `json:"specs" form:"specs"`                           // 规格
	DetailH5         string              `json:"detail_h5" form:"detail_h5"`                   // 商品详情
	ImageInfos       []string            `json:"image_infos" form:"image_infos"`               // 图片详情
	ItemClassifyInfo []*ItemClassifyInfo `json:"item_classify_info" form:"item_classify_info"` // 分类详情
	IpInfo           []*IpInfo           `json:"ip_info" form:"ip_info"`                       // IP详情
	TrademarkInfo    []*TrademarkInfo    `json:"trademark_info" form:"trademark_info"`         // 品牌详情
}

type ItemClassifyInfo struct {
	ID   string `json:"_id" form:"_id"`
	Name string `json:"name" form:"name"`
}

type IpInfo struct {
	ID   string `json:"_id" form:"_id"`
	Name string `json:"name" form:"name"`
}

type TrademarkInfo struct {
	ID    string `json:"_id" form:"_id"`
	Name  string `json:"name" form:"name"`
	Icon  string `json:"icon" form:"icon"`
	Level int32  `json:"level" form:"level"`
}

type GetSteamItemInfoByIdsData struct {
	Total int32            `json:"total" form:"total"`
	List  []*SteamItemInfo `json:"list" form:"list"`
}

type GetSteamItemInfoByIdsRsp struct {
	Code int32                     `json:"code" form:"code"`
	Desc string                    `json:"desc" form:"desc"`
	Data GetSteamItemInfoByIdsData `json:"data" form:"data"`
}

func GetSteamItemInfoByIds(ctx context.Context, ids []string) ([]*SteamItemInfo, error) {
	rsp := &GetSteamItemInfoByIdsRsp{}
	req, err := request.Tmt()
	if err != nil {
		return nil, err
	}
	err = req.Call(
		ctx,
		"open/steam_item/v1/get_steam_items_for_mall",
		&rsp,
		utilRequest.WithParams(map[string]interface{}{"steam_item_ids": ids}),
		utilRequest.WithMethodPost(),
	)
	if err != nil {
		return nil, err
	}

	if rsp.Code != 0 {
		return nil, response.Fail.SetMsg(rsp.Desc)
	}
	return rsp.Data.List, err
}
