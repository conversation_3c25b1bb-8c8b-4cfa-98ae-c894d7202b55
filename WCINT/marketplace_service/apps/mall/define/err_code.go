package define

import "e.coding.net/g-dtay0385/common/go-util/response"

var (
	MS200001Err = response.NewError(200001, "商品不存在")
	MS200002Err = response.NewError(200002, "当前用户购买此商品次数已达上限")
	MS200003Err = response.NewError(200003, "支付金额有误")
	MS200004Err = response.NewError(200004, "库存不足")
	MS200005Err = response.NewError(200005, "云仓商品不存在")
	MS200006Err = response.NewError(200006, "云仓商品未开启供应")
	MS200007Err = response.NewError(200007, "云仓商品库存不足")
	MS200008Err = response.NewError(200008, "商品上架中不允许更换SKU商品")
	MS200009Err = response.NewError(200009, "同一SKU仅允许一个直购商品处于上架状态")
	MS200010Err = response.NewError(200010, "每日限购数量不能超过总限购数量")
	MS200011Err = response.NewError(200011, "别心急，未到商品开始时间~")
	MS200012Err = response.NewError(200012, "嗷哦，商品下架了~")
	MS200013Err = response.NewError(200013, "限购 %d 件，不能购买更多了哦~")
	MS200014Err = response.NewError(200014, "每日限购 %d 件哦~")
	MS200015Err = response.NewError(200015, "订单不存在")
)
