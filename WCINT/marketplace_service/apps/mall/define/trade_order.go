package define

import (
	"gorm.io/datatypes"
	"marketplace_service/pkg/pagination"
	"time"
)

type (
	GetAdminTradeOrderListReq struct {
		pagination.Pagination
		ID                int64      `form:"id,string" json:"id,string" search:"type:eq;column:id;table:trade_order"`                    // 订单号
		OrderStatus       *int32     `form:"order_status" json:"order_status" search:"type:eq;column:order_status;table:trade_order"`    // 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
		UserId            string     `form:"user_id" json:"user_id" search:"type:eq;column:user_id;table:trade_order"`                   // 用户ID
		CreatedAtGte      *time.Time `form:"created_at_gte" json:"created_at_gte" search:"type:gte;column:created_at;table:trade_order"` // 创建时间开始
		CreatedAtLte      *time.Time `form:"created_at_lte" json:"created_at_lte" search:"type:lte;column:created_at;table:trade_order"` // 创建时间结束
		PayOrderId        string     `form:"pay_order_id" json:"pay_order_id" search:"type:eq;column:pay_order_id;table:trade_order"`    // 支付订单ID
		TradeOrderItem    `search:"type:join;column:id;table:trade_order;on:order_id;join:trade_order_item"`
		TradeOrderFreight `search:"type:join;column:id;table:trade_order;on:order_id;join:trade_order_freight"`
	}

	TradeOrderItem struct {
		MallItemID int64  `form:"mall_item_id,string" json:"mall_item_id,string" search:"type:eq;column:mall_item_id;table:trade_order_item"` // 直购商品ID
		SkuID      string `form:"sku_id" json:"sku_id" search:"type:eq;column:sku_id;table:trade_order_item"`                                 // 商品 sku_id
		ItemID     string `form:"item_id" json:"item_id" search:"type:eq;column:item_id;table:trade_order_item"`                              // 商品ID
		ItemName   string `form:"item_name" json:"item_name" search:"type:like;column:item_name;table:trade_order_item"`                      // 商品名称
	}

	TradeOrderFreight struct {
		DeliveryNumber string `form:"delivery_number" json:"delivery_number" search:"type:eq;column:delivery_number;table:trade_order_freight"`
	}

	GetAdminTradeOrderListResp struct {
		List  []*GetAdminTradeOrderData `json:"list"`
		Total int64                     `json:"total"`
	}
	GetAdminTradeOrderData struct {
		ID            int64                         `json:"id,string"`      // 订单ID
		OrderStatus   int32                         `json:"order_status"`   // 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
		UserId        string                        `json:"user_id"`        // 用户ID
		UserNickname  string                        `json:"user_nickname"`  // 用户昵称
		TotalAmount   int64                         `json:"total_amount"`   // 总金额
		PayAmount     int64                         `json:"pay_amount"`     // 支付金额
		FreightAmount int64                         `json:"freight_amount"` // 运费
		CreatedAt     time.Time                     `json:"created_at"`     // 创建时间
		OrderItems    []*GetAdminTradeOrderItemData `json:"order_items"`    // 订单子单列表
	}
)

type GetAdminTradeOrderItemData struct {
	MallItemID     int64  `json:"id,string"`       // 直购商品ID
	ItemID         string `json:"item_id"`         // 商品ID
	SkuID          string `json:"sku_id"`          // 商品 sku_id
	ItemIconURL    string `json:"item_icon_url"`   // 商品主图
	ItemName       string `json:"item_name"`       // 商品名称
	TrademarkID    string `json:"trademark_id"`    // 商品品牌id
	CategoryID     string `json:"category_id"`     // 商品分类id
	IPID           string `json:"ip_id"`           // 商品 ip_id
	TrademarkName  string `json:"brand_name"`      // 商品品牌名称
	CategoryName   string `json:"category_name"`   // 商品分类名称
	IPName         string `json:"ip_name"`         // 商品IP名称
	ItemSpecs      string `json:"item_specs"`      // 商品属性
	SalePrice      int64  `json:"sale_price"`      // 单价
	DiscountAmount int64  `json:"discount_amount"` // 商品享受的优惠金额
	Quantity       int32  `json:"quantity"`        // 购买数量
	FreightAmount  int64  `json:"freight_amount"`  // 运费
}

type (
	GetAdminTradeOrderDetailReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"`
	}

	GetAdminTradeOrderDetailFreight struct {
		OrderItemID     int64           `json:"order_item_id,string"` // 订单子单ID
		DeliveryNumber  string          `json:"delivery_number"`      // 快递单号
		DeliveryCompany string          `json:"delivery_company"`     // 快递公司
		DeliveredAt     *time.Time      `json:"delivered_at"`         // 发货时间
		Records         *datatypes.JSON `json:"records"`              // 物流信息
	}

	GetAdminTradeOrderDetailResp struct {
		ID                 int64                              `json:"id,string"`                    // 订单ID
		OrderType          int32                              `json:"order_type"`                   // 订单类型（1=直购订单）
		UserID             string                             `json:"user_id"`                      // 用户ID
		UserNickname       string                             `json:"user_nickname"`                // 用户昵称
		OrderStatus        int32                              `json:"order_status"`                 // 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
		PaymentStatus      int32                              `json:"payment_status"`               // 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
		ShippingStatus     int32                              `json:"shipping_status"`              // 发货状态（0=未发货，1=已发货，2=已签收）
		TotalAmount        int64                              `json:"total_amount"`                 // 总金额
		PayAmount          int64                              `json:"pay_amount"`                   // 支付金额
		FreightAmount      int64                              `json:"freight_amount"`               // 运费
		DiscountAmount     int64                              `json:"discount_amount"`              // 折扣金额
		ConsigneeName      string                             `json:"consignee_name"`               // 收货人姓名
		ConsigneePhone     string                             `json:"consignee_phone"`              // 收货人电话
		ConsigneeAddress   *datatypes.JSON                    `json:"consignee_address"`            // 收货地址
		CreatedAt          time.Time                          `json:"created_at"`                   // 下单时间
		PaymentTime        *time.Time                         `json:"payment_time"`                 // 支付时间
		PaymentMethod      string                             `json:"payment_method"`               // 支付方式
		PayOrderID         string                             `json:"pay_order_id"`                 // 支付订单ID
		DeliveredAt        *time.Time                         `json:"delivered_at"`                 // 发货时间
		SupplyChainOrderId string                             `json:"supply_chain_order_id,string"` // 供应链ID
		CancelAt           *time.Time                         `json:"cancel_at"`                    // 取消时间
		FinishedAt         *time.Time                         `json:"finished_at"`                  // 完成时间
		UserRemark         string                             `json:"user_remark"`                  // 用户备注
		CancelType         int32                              `json:"cancel_type"`                  // 取消类型（1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）
		Terminal           string                             `json:"terminal"`                     // 订单渠道
		OrderItems         []*GetAdminTradeOrderItemData      `json:"order_items"`                  // 物品列表
		FreightInfos       []*GetAdminTradeOrderDetailFreight `json:"freight_infos"`                // 物流信息

	}
)

type (
	GetTradeOrderDetailReq struct {
		ID     int64  `form:"id,string" json:"id,string" binding:"required"`
		UserId string `form:"user_id" json:"user_id" binding:"required"`
	}

	GetTradeOrderDetailResp struct {
		PayAmount     int64 `json:"pay_amount"`     // 支付金额(单位:分)
		FreightAmount int64 `json:"freight_amount"` // 运费金额(单位:分)
		PaymentStatus int32 `json:"payment_status"` // 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
	}
)

type (
	TradeOrderPaySuccessReq struct {
		ID              int64  `form:"id,string" json:"id,string" binding:"required"`
		RechargeOrderID string `form:"recharge_order_id" json:"recharge_order_id" binding:"required"`
		PayMethod       string `form:"pay_method" json:"pay_method"`
		PayTime         string `form:"pay_time" json:"pay_time"`
	}

	TradeOrderPaySuccessResp struct {
	}
)

type (
	TradeOrderRefundReq struct {
		RechargeOrderID string `form:"recharge_order_id" json:"recharge_order_id" binding:"required"`
		Amount          int64  `form:"amount" json:"amount" binding:"required"`
	}

	TradeOrderRefundResp struct {
		Amount int64 `form:"amount" json:"amount"`
	}
)

type OrderItemPriceDetail struct {
	MallItemID     int64  `json:"mall_item_id"`
	ItemID         string `json:"item_id"`
	Price          int64  `json:"price"`           // 原价（单位：分）
	DiscountPrice  int64  `json:"discount_price"`  // 折扣价（单位：分）
	Quantity       int32  `json:"quantity"`        // 购买数量
	Subtotal       int64  `json:"subtotal"`        // 小计 = price * quantity
	DiscountAmount int64  `json:"discount_amount"` // 折扣金额
	PayAmount      int64  `json:"pay_amount"`      // 实际支付金额
}

type CalculateOrderPriceResp struct {
	TotalAmount    int64                `json:"total_amount"`    // 总价
	DiscountAmount int64                `json:"discount_amount"` // 所有折扣总和
	FreightAmount  int64                `json:"freight_amount"`  // 运费
	PayAmount      int64                `json:"pay_amount"`      // 最终应支付金额
	OrderItem      OrderItemPriceDetail `json:"order_items"`     // 每个商品的明细
}

type OrderConsigneeAddress struct {
	Code  string `json:"code"`  // 邮政编码
	Area  string `json:"area"`  // 地区
	Place string `json:"place"` // 详细地址
}

type (
	GetWebTradeOrderListReq struct {
		pagination.Pagination
		OrderStatus *int32 `form:"order_status" json:"order_status"` // 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
	}

	GetWebTradeOrderListResp struct {
		List  []*GetWebTradeOrderData `json:"list"`
		Total int64                   `json:"total"`
	}

	GetWebTradeOrderData struct {
		ID              int64                            `json:"id,string"`        //id
		OrderStatus     int32                            `json:"order_status"`     // 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
		PaymentStatus   int32                            `json:"payment_status"`   // 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
		ShippingStatus  int32                            `json:"shipping_status"`  // 发货状态（0=未发货，1=已发货，2=已签收）
		TotalAmount     int64                            `json:"total_amount"`     // 总金额
		PayAmount       int64                            `json:"pay_amount"`       // 支付金额
		OrderItems      []*GetWebTradeOrderItemData      `json:"order_items"`      // 物品列表
		CountdownSecond int32                            `json:"countdown_second"` // 倒计时秒数
		FreightInfo     []*GetWebTradeOrderDetailFreight `json:"freight_info"`     // 物流信息
	}
)
type GetWebTradeOrderItemData struct {
	MallItemID  int64  `json:"id,string"`     // 直购商品ID
	ItemID      string `json:"item_id"`       // 商品ID
	ItemIconURL string `json:"item_icon_url"` // 商品主图
	ItemName    string `json:"item_name"`     // 商品名称
	SalePrice   int64  `json:"sale_price"`    // 单价
	Quantity    int32  `json:"quantity"`      // 购买数量
	ItemSpecs   string `json:"item_specs"`    // 商品属性
}

type (
	GetWebTradeOrderDetailReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"` // 订单id
	}

	GetWebTradeOrderDetailResp struct {
		ID               int64                            `json:"id,string"`         //id
		OrderStatus      int32                            `json:"order_status"`      // 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
		PaymentStatus    int32                            `json:"payment_status"`    // 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
		ShippingStatus   int32                            `json:"shipping_status"`   // 发货状态（0=未发货，1=已发货，2=已签收）
		TotalAmount      int64                            `json:"total_amount"`      // 总金额
		PayAmount        int64                            `json:"pay_amount"`        // 支付金额
		FreightAmount    int64                            `json:"freight_amount"`    // 运费
		DiscountAmount   int64                            `json:"discount_amount"`   // 折扣金额
		ConsigneeName    string                           `json:"consignee_name"`    // 收货人姓名
		ConsigneePhone   string                           `json:"consignee_phone"`   // 收货人电话
		ConsigneeAddress *datatypes.JSON                  `json:"consignee_address"` // 收货地址
		CreatedAt        time.Time                        `json:"created_at"`        // 下单时间
		PaymentTime      *time.Time                       `json:"payment_time"`      // 支付时间
		PaymentMethod    string                           `json:"payment_method"`    // 支付方式
		UserRemark       string                           `json:"user_remark"`       // 用户备注
		CountdownSecond  int32                            `json:"countdown_second"`  // 倒计时秒数
		CancelType       int32                            `json:"cancel_type"`       // 取消类型（1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）
		FinishedSecond   int32                            `json:"finished_second"`   // 订单完成倒计时秒数
		OrderItems       []*GetWebTradeOrderItemData      `json:"order_items"`       // 物品列表
		FreightInfo      []*GetWebTradeOrderDetailFreight `json:"freight_info"`      // 物流信息
	}

	GetWebTradeOrderDetailFreight struct {
		DeliveryNumber  string `json:"delivery_number"`  // 快递单号
		DeliveryCompany string `json:"delivery_company"` // 快递公司
	}
)

type (
	GetWebTradeOrderStatusStatReq struct {
	}

	GetWebTradeOrderStatusStatResp struct {
		List []*GetWebTradeOrderStatusStatData `json:"list"`
	}

	GetWebTradeOrderStatusStatData struct {
		OrderStatus int32 `json:"order_status"` // 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
		Count       int64 `json:"count"`
	}
)

type (
	CancelTradeOrderReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"` // 订单id
	}

	CancelTradeOrderResp struct {
	}
)

type (
	UpdateTradeOrderAddressReq struct {
		ID        int64  `json:"id,string" binding:"required"`  // 订单id
		AddressID string `json:"address_id" binding:"required"` // 地址ID
	}

	UpdateTradeOrderAddressResp struct {
	}
)

type (
	DelTradeOrderReq struct {
		ID int64 `json:"id,string" binding:"required"` // 订单id
	}

	DelTradeOrderResp struct {
	}
)

type (
	ConfirmTradeOrderReq struct {
		ID int64 `json:"id,string" binding:"required"` // 订单id
	}

	ConfirmTradeOrderResp struct {
	}
)

type (
	TradeOrderTimeoutCloseReq struct{}
)

type (
	SupplyChainNoticeOriginalReq struct {
		AppKey     string `json:"app_key" binding:"required"`
		Format     string `json:"format" binding:"required"`
		Timestamp  int64  `json:"timestamp" binding:"required"`
		Version    string `json:"version" binding:"required"`
		BizContent string `json:"biz_content" binding:"required"`
		Sign       string `json:"sign" binding:"required"`
	}

	SupplyChainNoticeReq struct {
		Type int32                    `json:"type" binding:"required"`
		Data []map[string]interface{} `json:"data"`
	}

	UpdateTradeOrderDeliver struct {
		Mid         int64     `json:"mid"`
		CNo         string    `json:"c_no"`
		SkuNo       string    `json:"sku_no"`
		FreightNo   string    `json:"freight_no"`
		FreightTime time.Time `json:"freight_time"`
	}

	UpdateTradeOrderUpdateFreight struct {
		Mid         int64     `json:"mid"`
		CNo         string    `json:"c_no"`
		SkuNo       string    `json:"sku_no"`
		FreightNo   string    `json:"freight_no"`
		FreightTime time.Time `json:"freight_time"`
	}

	UpdateTradeOrderFreightInfo struct {
		Mid       int64  `json:"mid"`
		Com       string `json:"com"`
		CNo       string `json:"c_no"`
		FreightNo string `json:"freight_no"`
		Checked   int32  `json:"checked"`
		Status    int32  `json:"status"`
		Info      string `json:"info"`
	}

	SupplyChainNoticeResp struct {
		Code int32  `json:"code"`
		Desc string `json:"desc"`
		Data struct {
			Failed []int64 `json:"failed"` // 失败的消息ID（请求参数中的"mid"）
		} `json:"data"`
	}
)

type (
	TradeOrderFailedRetryUploadToSupplyReq struct{}
)
type (
	TradeOrderFinishReq struct {
	}
	TradeOrderFinishResp struct {
	}
)

type (
	GetWebTradeOrderFreightInfoReq struct {
		ID             int64  `form:"id,string" json:"id,string" binding:"required"`             // 订单id
		DeliveryNumber string `form:"delivery_number" json:"delivery_number" binding:"required"` // 快递单号
	}

	GetWebTradeOrderFreightInfoResp struct {
		DeliveryNumber  string          `json:"delivery_number"`  // 快递单号
		DeliveryCompany string          `json:"delivery_company"` // 快递公司
		Status          int32           `json:"status"`
		Checked         int32           `json:"checked"` // 0未签收 1已签收
		Records         *datatypes.JSON `json:"records"`
	}

	GetWebTradeOrderFreightRecords struct {
		Time     string  `json:"time"`      // 时间
		Context  string  `json:"context"`   //  内容
		Ftime    string  `json:"ftime"`     //  时间
		AreaCode *string `json:"area_code"` // 地区编码
		AreaName *string `json:"area_name"` // 地址信息
		Status   string  `json:"status"`    // 状态描述
	}
)

type (
	CancelAdminTradeOrderReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"` // 订单id
	}

	CancelAdminTradeOrderResp struct {
	}
)

type (
	SyncAdminTradeOrderFreightReq struct {
		OrderItemId int64 `form:"id,string" json:"id,string" binding:"required"` // 订单详情ID
	}

	SyncAdminTradeOrderFreightResp struct {
	}
)

type (
	AdminGetOrderPrivateInfoReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"` // 订单id
	}
	AdminGetOrderPrivateInfoResp struct {
		ConsigneePhone string `json:"consignee_phone"` // 收货人电话
	}
)
