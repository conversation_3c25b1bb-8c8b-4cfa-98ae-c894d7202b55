package enums

// MallItemStatus 直购商品状态
type MallItemStatus int32

func (r MallItemStatus) Val() int32 {
	return int32(r)
}

const (
	// MallItemStatusDel 已删除
	MallItemStatusDel MallItemStatus = -1
	// MallItemStatusWaiting 待上架
	MallItemStatusWaiting MallItemStatus = 0
	// MallItemStatusUp 已上架
	MallItemStatusUp MallItemStatus = 1
	// MallItemStatusDown 已下架
	MallItemStatusDown MallItemStatus = 2
)

// MallItemOrderByType 直购商品列表排序类型
type MallItemOrderByType int32

func (r MallItemOrderByType) Val() int32 {
	return int32(r)
}

const (
	// MallItemOrderByTypeDefault 默认排序
	MallItemOrderByTypeDefault MallItemOrderByType = 1
	// MallItemOrderByTypeSaleVolume 销量排序
	MallItemOrderByTypeSaleVolume MallItemOrderByType = 2
	// MallItemOrderByTypePrice 价格排序
	MallItemOrderByTypePrice MallItemStatus = 3
)
