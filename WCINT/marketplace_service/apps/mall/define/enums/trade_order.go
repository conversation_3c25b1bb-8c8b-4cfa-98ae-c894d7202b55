package enums

// OrderType 订单类型
type OrderType int32

func (s OrderType) Val() int32 {
	return int32(s)
}

const (
	// OrderTypeMall 直购
	OrderTypeMall OrderType = 1
)

// OrderStatus 订单状态
type OrderStatus int32

func (s OrderStatus) Val() int32 {
	return int32(s)
}

const (
	// OrderStatusDel 已删除
	OrderStatusDel OrderStatus = -1
	// OrderStatusUnPaid 未支付
	OrderStatusUnPaid OrderStatus = 0
	// OrderStatusUnDelivered 待发货
	OrderStatusUnDelivered OrderStatus = 10
	// OrderStatusDelivered 已发货
	OrderStatusDelivered OrderStatus = 20
	// OrderStatusCompleted 已完成
	OrderStatusCompleted OrderStatus = 30
	// OrderStatusCanceled 已取消
	OrderStatusCanceled OrderStatus = 40
)

var OrderStatusForUserList = []int32{
	OrderStatusUnPaid.Val(),
	OrderStatusUnDelivered.Val(),
	OrderStatusDelivered.Val(),
}

// PaymentStatus 支付状态
type PaymentStatus int32

func (s PaymentStatus) Val() int32 {
	return int32(s)
}

const (
	// PaymentStatusUnPaid 未支付
	PaymentStatusUnPaid PaymentStatus = 0
	// PaymentStatusPaid 已支付
	PaymentStatusPaid PaymentStatus = 1
	// PaymentStatusRefund 退款
	PaymentStatusRefund PaymentStatus = 2
	// PaymentStatusRefunded 全部退款
	PaymentStatusRefunded PaymentStatus = 3
)

// ShippingStatus 订单发货状态
type ShippingStatus int32

func (s ShippingStatus) Val() int32 {
	return int32(s)
}

const (
	// ShippingStatusNotShipped 未发货
	ShippingStatusNotShipped ShippingStatus = 0
	// ShippingStatusShipped 已发货
	ShippingStatusShipped ShippingStatus = 1
	// ShippingStatusSigned 收货人已签收
	ShippingStatusSigned ShippingStatus = 2
	// ShippingStatusReturned 已退货（或退回中）
	ShippingStatusReturned ShippingStatus = 3
)

// CancelType 取消类型
type CancelType int32

func (s CancelType) Val() int32 {
	return int32(s)
}

const (
	// CancelTypeUser 用户主动取消
	CancelTypeUser CancelType = 1
	// CancelTypeTimeOut 系统超时取消
	CancelTypeTimeOut CancelType = 2
	// CancelTypeWC 文潮管理端取消
	CancelTypeWC CancelType = 3
	// CancelTypeYC 云仓管理端取消
	CancelTypeYC CancelType = 4
)

// IsUserDel 用户是否删除订单
type IsUserDel int32

func (s IsUserDel) Val() int32 {
	return int32(s)
}

const (
	IsUserDelYes IsUserDel = 1
	IsUserDelNo  IsUserDel = 0
)
