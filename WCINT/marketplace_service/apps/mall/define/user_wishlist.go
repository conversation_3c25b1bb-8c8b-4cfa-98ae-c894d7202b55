package define

import "marketplace_service/pkg/pagination"

type (
	WebWishlistListReq struct {
		pagination.Pagination
	}
	WebWishlistListResp struct {
		List    []*WebWishlistListData `json:"list"`
		HasMore bool                   `json:"has_more"` // 判断当前页是否为最后一页
	}
	WebWishlistListData struct {
		ID            int64  `json:"id,string"`      // 用户想要ID
		MallItemID    int64  `json:"mall_item_id"`   // 直购商品ID
		ItemID        string `json:"item_id"`        // 商品ID
		ItemName      string `json:"item_name"`      // 商品名称
		IconUrl       string `json:"item_icon_url"`  // 商品主图
		SalePrice     int64  `json:"sale_price"`     // 售价
		Discount      *int32 `json:"discount"`       // 折扣
		DiscountPrice int64  `json:"discount_price"` // 折扣价
	}
)
type (
	WebWishlistReq struct {
		ItemID string `form:"item_id" json:"item_id" binding:"required"`
	}
)
