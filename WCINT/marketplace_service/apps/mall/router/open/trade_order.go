package open

import (
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/api/open"
)

// TradeOrder 交易订单open相关
func TradeOrder(router *gin.RouterGroup) {
	group := router.Group("/trade_order")
	{
		// 获取订单详情
		group.GET("/detail", open.GetTradeOrderDetail)
		// 订单支付成功回调
		group.POST("/pay_success", open.TradeOrderPaySuccess)
		// 订单支付超时关闭
		group.POST("/timeout_close", open.TradeOrderTimeoutClose)
		// 失败订单补偿上传到供应链
		group.POST("/retry_upload_to_supply", open.TradeOrderFailedRetryUploadToSupply)
		// 订单支付自动收货
		group.POST("/finish", open.TradeOrderFinish)
	}
}
