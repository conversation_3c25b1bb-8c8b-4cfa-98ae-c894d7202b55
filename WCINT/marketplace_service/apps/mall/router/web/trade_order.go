package web

import (
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/api/web"
)

// TradeOrder 交易订单web相关
func TradeOrder(router *gin.RouterGroup) {
	group := router.Group("/trade_order")
	{
		// 查询直购订单列表
		group.GET("/list", web.GetWebTradeOrderList)
		// 查询直购订单详情
		group.GET("/detail", web.GetWebTradeOrderDetail)
		// 查询直购订单详情
		group.GET("/status_stat", web.GetWebTradeOrderStatusStat)
		// 取消订单
		group.POST("/cancel", web.CancelTradeOrder)
		// 修改地址
		group.POST("/update_address", web.UpdateTradeOrderAddress)
		// 删除订单
		group.POST("/del", web.DelTradeOrder)
		// 确定收货
		group.POST("/confirm", web.ConfirmTradeOrder)
		// 查询直购订单物流信息
		group.GET("/freight_info", web.GetWebTradeOrderFreightInfo)
	}
}
