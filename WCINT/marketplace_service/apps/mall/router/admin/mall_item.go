package admin

import (
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/api/admin"
)

// MallItem 直购商品相关路由
func MallItem(router *gin.RouterGroup) {
	group := router.Group("/mall_item")
	{
		// 添加配置
		group.POST("/add", admin.AddMallItem)
		// 获取配置列表
		group.GET("/list", admin.GetMallItemList)
		// 获取配置详情
		group.GET("/detail", admin.GetMallItemDetail)
		// 编辑配置
		group.POST("/edit", admin.EditMallItem)
		// 编辑优先级
		group.POST("/edit_priority", admin.EditMallItemPriority)
		// 编辑状态
		group.POST("/edit_status", admin.EditMallItemStatus)
	}
}
