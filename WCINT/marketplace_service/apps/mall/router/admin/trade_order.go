package admin

import (
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/api/admin"
)

// TradeOrder 交易订单管理端相关
func TradeOrder(router *gin.RouterGroup) {
	group := router.Group("/trade_order")
	{
		// 获取订单列表
		group.GET("/list", admin.GetTradeOrderList)
		// 获取订单详情
		group.GET("/detail", admin.GetTradeOrderDetail)
		// 订单发起退款
		group.POST("/refund", admin.TradeOrderRefund)
		// 取消订单
		group.POST("/cancel", admin.CancelAdminTradeOrder)
		// 同步订单物流信息
		group.POST("/sync_freight", admin.SyncAdminTradeOrderFreight)
		// 获取订单隐私信息
		group.GET("/private_info", admin.GetOrderPrivateInfo)
	}
}
