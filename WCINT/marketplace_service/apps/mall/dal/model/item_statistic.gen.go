// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameItemStatistic = "item_statistic"

// ItemStatistic 商城直购商品统计表表
type ItemStatistic struct {
	ID            int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                   // 主键
	SkuID         string                `gorm:"column:sku_id;type:varchar(32);not null;comment:商品 sku_id" json:"sku_id"`                                 // 商品 sku_id
	ItemID        string                `gorm:"column:item_id;type:varchar(32);not null;comment:商品ID" json:"item_id"`                                    // 商品ID
	SalesVolume   int32                 `gorm:"column:sales_volume;type:int;not null;comment:销量" json:"sales_volume"`                                    // 销量
	WishlistCount int32                 `gorm:"column:wishlist_count;type:int;not null;comment:想要数量" json:"wishlist_count"`                              // 想要数量
	CreatedAt     time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt     time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel         soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除（0=未删除，1=删除）;softDelete:flag" json:"is_del"`           // 是否删除（0=未删除，1=删除）
}

// TableName ItemStatistic's table name
func (*ItemStatistic) TableName() string {
	return TableNameItemStatistic
}
