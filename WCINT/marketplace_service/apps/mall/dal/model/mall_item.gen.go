// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameMallItem = "mall_item"

// MallItem 商城直购商品表
type MallItem struct {
	ID             int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                   // 主键
	Status         int32                 `gorm:"column:status;type:tinyint;not null;comment:状态（0=待上架，1=已上架，2=已下架）" json:"status"`                         // 状态（0=待上架，1=已上架，2=已下架）
	ItemID         string                `gorm:"column:item_id;type:varchar(32);not null;comment:商品 id" json:"item_id"`                                   // 商品 id
	ItemName       string                `gorm:"column:item_name;type:varchar(255);not null;comment:商品名称" json:"item_name"`                               // 商品名称
	SkuID          string                `gorm:"column:sku_id;type:varchar(32);not null;comment:商品 sku_id" json:"sku_id"`                                 // 商品 sku_id
	SpuID          string                `gorm:"column:spu_id;type:varchar(32);not null;comment:商品 spu_id" json:"spu_id"`                                 // 商品 spu_id
	IPID           string                `gorm:"column:ip_id;type:varchar(32);comment:商品 ip_id" json:"ip_id"`                                             // 商品 ip_id
	TrademarkID    string                `gorm:"column:trademark_id;type:varchar(32);comment:商品品牌id" json:"trademark_id"`                                 // 商品品牌id
	CategoryID     string                `gorm:"column:category_id;type:varchar(32);comment:商品分类id" json:"category_id"`                                   // 商品分类id
	ItemSpecs      string                `gorm:"column:item_specs;type:varchar(255);comment:商品属性" json:"item_specs"`                                      // 商品属性
	ItemIconURL    string                `gorm:"column:item_icon_url;type:varchar(255);comment:商品主图" json:"item_icon_url"`                                // 商品主图
	SalePrice      int64                 `gorm:"column:sale_price;type:bigint;not null;comment:售价(分)" json:"sale_price"`                                  // 售价(分)
	DiscountPrice  *int64                `gorm:"column:discount_price;type:bigint;comment:折扣价(分）" json:"discount_price"`                                  // 折扣价(分）
	Discount       *int32                `gorm:"column:discount;type:tinyint;comment:折扣" json:"discount"`                                                 // 折扣
	Freight        int64                 `gorm:"column:freight;type:bigint;not null;comment:运费(分)" json:"freight"`                                        // 运费(分)
	StockType      int32                 `gorm:"column:stock_type;type:tinyint;not null;default:1;comment:库存类型（1=限定库存，2=同步商品库存）" json:"stock_type"`       // 库存类型（1=限定库存，2=同步商品库存）
	Stock          int32                 `gorm:"column:stock;type:int;not null;comment:限定库存量" json:"stock"`                                               // 限定库存量
	AvailableStock int32                 `gorm:"column:available_stock;type:int;not null;comment:可用库存" json:"available_stock"`                            // 可用库存
	TotalLimit     int32                 `gorm:"column:total_limit;type:int;not null;comment:总限购数量" json:"total_limit"`                                   // 总限购数量
	DailyLimit     int32                 `gorm:"column:daily_limit;type:int;not null;comment:每日限购数量" json:"daily_limit"`                                  // 每日限购数量
	StartTime      time.Time             `gorm:"column:start_time;type:datetime(3);not null;comment:开始展示时间" json:"start_time"`                            // 开始展示时间
	Priority       int32                 `gorm:"column:priority;type:int;not null;comment:优先级" json:"priority"`                                           // 优先级
	SalesVolume    int32                 `gorm:"column:sales_volume;type:int;not null;comment:销量" json:"sales_volume"`                                    // 销量
	WishlistCount  int32                 `gorm:"column:wishlist_count;type:int;not null;comment:想要数量" json:"wishlist_count"`                              // 想要数量
	ShelfTime      *time.Time            `gorm:"column:shelf_time;type:datetime(3);comment:上架时间" json:"shelf_time"`                                       // 上架时间
	CreatedBy      string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                        // 创建人
	CreatedAt      time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedBy      string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                        // 更新人
	UpdatedAt      time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel          soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName MallItem's table name
func (*MallItem) TableName() string {
	return TableNameMallItem
}
