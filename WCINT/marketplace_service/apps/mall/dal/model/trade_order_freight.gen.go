// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/plugin/soft_delete"
)

const TableNameTradeOrderFreight = "trade_order_freight"

// TradeOrderFreight 商城直购订单物流表
type TradeOrderFreight struct {
	ID              int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                                                             // 主键
	OrderID         int64                 `gorm:"column:order_id;type:bigint;not null;comment:订单ID" json:"order_id"`                                                                                 // 订单ID
	OrderItemID     int64                 `gorm:"column:order_item_id;type:bigint;not null;comment:订单详情ID" json:"order_item_id"`                                                                     // 订单详情ID
	DeliveryNumber  string                `gorm:"column:delivery_number;type:varchar(100);comment:快递单号" json:"delivery_number"`                                                                      // 快递单号
	DeliveryCompany string                `gorm:"column:delivery_company;type:varchar(100);comment:快递公司" json:"delivery_company"`                                                                    // 快递公司
	Status          int32                 `gorm:"column:status;type:tinyint;not null;comment:状态（-1=无效，0=途中，1=揽收，2=疑难，3=签收，4=退签，5=派件，6=退回，7=转单，10=待清关，11=清关中，12=已清关，13=清关异常，14=收件人拒签）" json:"status"` // 状态（-1=无效，0=途中，1=揽收，2=疑难，3=签收，4=退签，5=派件，6=退回，7=转单，10=待清关，11=清关中，12=已清关，13=清关异常，14=收件人拒签）
	Checked         int32                 `gorm:"column:checked;type:tinyint;not null;comment:状态（0=未签收，1=已签收）" json:"checked"`                                                                       // 状态（0=未签收，1=已签收）
	DeliveredAt     *time.Time            `gorm:"column:delivered_at;type:datetime(3);comment:订单发货时间" json:"delivered_at"`                                                                           // 订单发货时间
	Records         *datatypes.JSON       `gorm:"column:records;type:json;comment:物流信息" json:"records"`                                                                                              // 物流信息
	CreatedBy       string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                                                                  // 创建人
	CreatedAt       time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`                                           // 创建时间
	UpdatedBy       string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                                                                  // 更新人
	UpdatedAt       time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`                                           // 更新时间
	IsDel           soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`                                                  // 是否删除【0->未删除; 1->删除】
}

// TableName TradeOrderFreight's table name
func (*TradeOrderFreight) TableName() string {
	return TableNameTradeOrderFreight
}
