// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameTradeOrderItem = "trade_order_item"

// TradeOrderItem 商城直购订单详情表
type TradeOrderItem struct {
	ID             int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                   // 主键
	OrderID        int64                 `gorm:"column:order_id;type:bigint;not null;comment:订单ID" json:"order_id"`                                       // 订单ID
	MallItemID     int64                 `gorm:"column:mall_item_id;type:bigint;not null;comment:直购商品ID" json:"mall_item_id"`                             // 直购商品ID
	ItemID         string                `gorm:"column:item_id;type:varchar(32);not null;comment:商品ID" json:"item_id"`                                    // 商品ID
	SkuID          string                `gorm:"column:sku_id;type:varchar(32);not null;comment:商品 sku_id" json:"sku_id"`                                 // 商品 sku_id
	SpuID          string                `gorm:"column:spu_id;type:varchar(32);not null;comment:商品 spu_id" json:"spu_id"`                                 // 商品 spu_id
	ItemIconURL    string                `gorm:"column:item_icon_url;type:varchar(255);comment:商品主图" json:"item_icon_url"`                                // 商品主图
	ItemSpecs      string                `gorm:"column:item_specs;type:varchar(255);comment:商品属性" json:"item_specs"`                                      // 商品属性
	ItemName       string                `gorm:"column:item_name;type:varchar(255);not null;comment:商品名称" json:"item_name"`                               // 商品名称
	SalePrice      int64                 `gorm:"column:sale_price;type:bigint;not null;comment:售价(分)" json:"sale_price"`                                  // 售价(分)
	DiscountPrice  int64                 `gorm:"column:discount_price;type:bigint;not null;comment:折扣价(分）" json:"discount_price"`                         // 折扣价(分）
	Quantity       int32                 `gorm:"column:quantity;type:int;not null;comment:购买数量" json:"quantity"`                                          // 购买数量
	Subtotal       int64                 `gorm:"column:subtotal;type:bigint;not null;comment:小计金额（= price * quantity）" json:"subtotal"`                   // 小计金额（= price * quantity）
	DiscountAmount int64                 `gorm:"column:discount_amount;type:bigint;not null;comment:商品享受的优惠金额" json:"discount_amount"`                    // 商品享受的优惠金额
	PayAmount      int64                 `gorm:"column:pay_amount;type:bigint;not null;comment:实付金额" json:"pay_amount"`                                   // 实付金额
	DeliveryStatus int32                 `gorm:"column:delivery_status;type:tinyint;not null;comment:发货状态" json:"delivery_status"`                        // 发货状态
	CreatedAt      time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt      time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel          soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName TradeOrderItem's table name
func (*TradeOrderItem) TableName() string {
	return TableNameTradeOrderItem
}
