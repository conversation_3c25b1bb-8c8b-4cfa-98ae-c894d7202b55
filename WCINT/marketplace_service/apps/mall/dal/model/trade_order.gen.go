// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/plugin/soft_delete"
)

const TableNameTradeOrder = "trade_order"

// TradeOrder 商城直购订单表
type TradeOrder struct {
	ID                 int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                               // 主键
	UserID             string                `gorm:"column:user_id;type:varchar(32);not null;comment:用户ID" json:"user_id"`                                                // 用户ID
	OrderType          int32                 `gorm:"column:order_type;type:tinyint;not null;comment:订单类型（1=直购订单）" json:"order_type"`                                      // 订单类型（1=直购订单）
	OrderStatus        int32                 `gorm:"column:order_status;type:tinyint;not null;comment:订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）" json:"order_status"`       // 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
	PaymentStatus      int32                 `gorm:"column:payment_status;type:tinyint;not null;comment:支付状态（0=未支付，1=已支付，2=退款，3=全额退款）" json:"payment_status"`             // 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
	ShippingStatus     int32                 `gorm:"column:shipping_status;type:tinyint;not null;comment:发货状态（0=未发货，1=已发货，2=已签收）" json:"shipping_status"`                 // 发货状态（0=未发货，1=已发货，2=已签收）
	TotalAmount        int64                 `gorm:"column:total_amount;type:bigint;not null;comment:总金额" json:"total_amount"`                                            // 总金额
	PayAmount          int64                 `gorm:"column:pay_amount;type:bigint;not null;comment:支付金额" json:"pay_amount"`                                               // 支付金额
	FreightAmount      int64                 `gorm:"column:freight_amount;type:bigint;not null;comment:运费" json:"freight_amount"`                                         // 运费
	DiscountAmount     int64                 `gorm:"column:discount_amount;type:bigint;not null;comment:折扣金额" json:"discount_amount"`                                     // 折扣金额
	PaymentMethod      string                `gorm:"column:payment_method;type:varchar(255);comment:支付方式" json:"payment_method"`                                          // 支付方式
	PaymentTime        *time.Time            `gorm:"column:payment_time;type:datetime;comment:支付时间" json:"payment_time"`                                                  // 支付时间
	PayOrderID         string                `gorm:"column:pay_order_id;type:varchar(32);comment:支付订单" json:"pay_order_id"`                                               // 支付订单
	AddressID          string                `gorm:"column:address_id;type:varchar(32);comment:用户地址ID" json:"address_id"`                                                 // 用户地址ID
	ConsigneeName      string                `gorm:"column:consignee_name;type:varchar(32);comment:收货人姓名" json:"consignee_name"`                                          // 收货人姓名
	ConsigneePhone     string                `gorm:"column:consignee_phone;type:varchar(32);comment:收货人电话" json:"consignee_phone"`                                        // 收货人电话
	ConsigneeAddress   *datatypes.JSON       `gorm:"column:consignee_address;type:json;comment:收货地址" json:"consignee_address"`                                            // 收货地址
	DeliveredAt        *time.Time            `gorm:"column:delivered_at;type:datetime(3);comment:订单发货时间" json:"delivered_at"`                                             // 订单发货时间
	FinishedAt         *time.Time            `gorm:"column:finished_at;type:datetime(3);comment:订单完成时间" json:"finished_at"`                                               // 订单完成时间
	CancelType         int32                 `gorm:"column:cancel_type;type:tinyint;not null;comment:取消类型（0=未取消，1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）" json:"cancel_type"` // 取消类型（0=未取消，1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）
	CancelAt           *time.Time            `gorm:"column:cancel_at;type:datetime(3);comment:订单取消时间" json:"cancel_at"`                                                   // 订单取消时间
	Terminal           string                `gorm:"column:terminal;type:varchar(255);not null;comment:订单来源渠道" json:"terminal"`                                           // 订单来源渠道
	UserIP             string                `gorm:"column:user_ip;type:varchar(30);not null;comment:用户 IP" json:"user_ip"`                                               // 用户 IP
	UserRemark         string                `gorm:"column:user_remark;type:varchar(255);comment:用户备注" json:"user_remark"`                                                // 用户备注
	SupplyChainOrderID string                `gorm:"column:supply_chain_order_id;type:varchar(32);comment:供应链订单ID" json:"supply_chain_order_id"`                          // 供应链订单ID
	IsUserDel          int32                 `gorm:"column:is_user_del;type:tinyint(1);not null;comment:用户是否删除订单" json:"is_user_del"`                                     // 用户是否删除订单
	CreatedAt          time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`             // 创建时间
	UpdatedAt          time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`             // 更新时间
	IsDel              soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`                    // 是否删除【0->未删除; 1->删除】
	TradeOrderItem     []*TradeOrderItem     `gorm:"foreignKey:order_id" json:"trade_order_item"`
}

// TableName TradeOrder's table name
func (*TradeOrder) TableName() string {
	return TableNameTradeOrder
}
