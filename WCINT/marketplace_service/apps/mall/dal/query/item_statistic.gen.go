// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newItemStatistic(db *gorm.DB, opts ...gen.DOOption) itemStatistic {
	_itemStatistic := itemStatistic{}

	_itemStatistic.itemStatisticDo.UseDB(db, opts...)
	_itemStatistic.itemStatisticDo.UseModel(&model.ItemStatistic{})

	tableName := _itemStatistic.itemStatisticDo.TableName()
	_itemStatistic.ALL = field.NewAsterisk(tableName)
	_itemStatistic.ID = field.NewInt64(tableName, "id")
	_itemStatistic.SkuID = field.NewString(tableName, "sku_id")
	_itemStatistic.ItemID = field.NewString(tableName, "item_id")
	_itemStatistic.SalesVolume = field.NewInt32(tableName, "sales_volume")
	_itemStatistic.WishlistCount = field.NewInt32(tableName, "wishlist_count")
	_itemStatistic.CreatedAt = field.NewTime(tableName, "created_at")
	_itemStatistic.UpdatedAt = field.NewTime(tableName, "updated_at")
	_itemStatistic.IsDel = field.NewField(tableName, "is_del")

	_itemStatistic.fillFieldMap()

	return _itemStatistic
}

// itemStatistic 商城直购商品统计表表
type itemStatistic struct {
	itemStatisticDo

	ALL           field.Asterisk
	ID            field.Int64  // 主键
	SkuID         field.String // 商品 sku_id
	ItemID        field.String // 商品ID
	SalesVolume   field.Int32  // 销量
	WishlistCount field.Int32  // 想要数量
	CreatedAt     field.Time   // 创建时间
	UpdatedAt     field.Time   // 更新时间
	IsDel         field.Field  // 是否删除（0=未删除，1=删除）

	fieldMap map[string]field.Expr
}

func (i itemStatistic) Table(newTableName string) *itemStatistic {
	i.itemStatisticDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i itemStatistic) As(alias string) *itemStatistic {
	i.itemStatisticDo.DO = *(i.itemStatisticDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *itemStatistic) updateTableName(table string) *itemStatistic {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewInt64(table, "id")
	i.SkuID = field.NewString(table, "sku_id")
	i.ItemID = field.NewString(table, "item_id")
	i.SalesVolume = field.NewInt32(table, "sales_volume")
	i.WishlistCount = field.NewInt32(table, "wishlist_count")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.IsDel = field.NewField(table, "is_del")

	i.fillFieldMap()

	return i
}

func (i *itemStatistic) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *itemStatistic) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["sku_id"] = i.SkuID
	i.fieldMap["item_id"] = i.ItemID
	i.fieldMap["sales_volume"] = i.SalesVolume
	i.fieldMap["wishlist_count"] = i.WishlistCount
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["is_del"] = i.IsDel
}

func (i itemStatistic) clone(db *gorm.DB) itemStatistic {
	i.itemStatisticDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i itemStatistic) replaceDB(db *gorm.DB) itemStatistic {
	i.itemStatisticDo.ReplaceDB(db)
	return i
}

type itemStatisticDo struct{ gen.DO }

type IItemStatisticDo interface {
	gen.SubQuery
	Debug() IItemStatisticDo
	WithContext(ctx context.Context) IItemStatisticDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IItemStatisticDo
	WriteDB() IItemStatisticDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IItemStatisticDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IItemStatisticDo
	Not(conds ...gen.Condition) IItemStatisticDo
	Or(conds ...gen.Condition) IItemStatisticDo
	Select(conds ...field.Expr) IItemStatisticDo
	Where(conds ...gen.Condition) IItemStatisticDo
	Order(conds ...field.Expr) IItemStatisticDo
	Distinct(cols ...field.Expr) IItemStatisticDo
	Omit(cols ...field.Expr) IItemStatisticDo
	Join(table schema.Tabler, on ...field.Expr) IItemStatisticDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IItemStatisticDo
	RightJoin(table schema.Tabler, on ...field.Expr) IItemStatisticDo
	Group(cols ...field.Expr) IItemStatisticDo
	Having(conds ...gen.Condition) IItemStatisticDo
	Limit(limit int) IItemStatisticDo
	Offset(offset int) IItemStatisticDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IItemStatisticDo
	Unscoped() IItemStatisticDo
	Create(values ...*model.ItemStatistic) error
	CreateInBatches(values []*model.ItemStatistic, batchSize int) error
	Save(values ...*model.ItemStatistic) error
	First() (*model.ItemStatistic, error)
	Take() (*model.ItemStatistic, error)
	Last() (*model.ItemStatistic, error)
	Find() ([]*model.ItemStatistic, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ItemStatistic, err error)
	FindInBatches(result *[]*model.ItemStatistic, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ItemStatistic) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IItemStatisticDo
	Assign(attrs ...field.AssignExpr) IItemStatisticDo
	Joins(fields ...field.RelationField) IItemStatisticDo
	Preload(fields ...field.RelationField) IItemStatisticDo
	FirstOrInit() (*model.ItemStatistic, error)
	FirstOrCreate() (*model.ItemStatistic, error)
	FindByPage(offset int, limit int) (result []*model.ItemStatistic, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IItemStatisticDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (i itemStatisticDo) Debug() IItemStatisticDo {
	return i.withDO(i.DO.Debug())
}

func (i itemStatisticDo) WithContext(ctx context.Context) IItemStatisticDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i itemStatisticDo) ReadDB() IItemStatisticDo {
	return i.Clauses(dbresolver.Read)
}

func (i itemStatisticDo) WriteDB() IItemStatisticDo {
	return i.Clauses(dbresolver.Write)
}

func (i itemStatisticDo) Session(config *gorm.Session) IItemStatisticDo {
	return i.withDO(i.DO.Session(config))
}

func (i itemStatisticDo) Clauses(conds ...clause.Expression) IItemStatisticDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i itemStatisticDo) Returning(value interface{}, columns ...string) IItemStatisticDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i itemStatisticDo) Not(conds ...gen.Condition) IItemStatisticDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i itemStatisticDo) Or(conds ...gen.Condition) IItemStatisticDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i itemStatisticDo) Select(conds ...field.Expr) IItemStatisticDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i itemStatisticDo) Where(conds ...gen.Condition) IItemStatisticDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i itemStatisticDo) Order(conds ...field.Expr) IItemStatisticDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i itemStatisticDo) Distinct(cols ...field.Expr) IItemStatisticDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i itemStatisticDo) Omit(cols ...field.Expr) IItemStatisticDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i itemStatisticDo) Join(table schema.Tabler, on ...field.Expr) IItemStatisticDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i itemStatisticDo) LeftJoin(table schema.Tabler, on ...field.Expr) IItemStatisticDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i itemStatisticDo) RightJoin(table schema.Tabler, on ...field.Expr) IItemStatisticDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i itemStatisticDo) Group(cols ...field.Expr) IItemStatisticDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i itemStatisticDo) Having(conds ...gen.Condition) IItemStatisticDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i itemStatisticDo) Limit(limit int) IItemStatisticDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i itemStatisticDo) Offset(offset int) IItemStatisticDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i itemStatisticDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IItemStatisticDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i itemStatisticDo) Unscoped() IItemStatisticDo {
	return i.withDO(i.DO.Unscoped())
}

func (i itemStatisticDo) Create(values ...*model.ItemStatistic) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i itemStatisticDo) CreateInBatches(values []*model.ItemStatistic, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i itemStatisticDo) Save(values ...*model.ItemStatistic) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i itemStatisticDo) First() (*model.ItemStatistic, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ItemStatistic), nil
	}
}

func (i itemStatisticDo) Take() (*model.ItemStatistic, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ItemStatistic), nil
	}
}

func (i itemStatisticDo) Last() (*model.ItemStatistic, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ItemStatistic), nil
	}
}

func (i itemStatisticDo) Find() ([]*model.ItemStatistic, error) {
	result, err := i.DO.Find()
	return result.([]*model.ItemStatistic), err
}

func (i itemStatisticDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ItemStatistic, err error) {
	buf := make([]*model.ItemStatistic, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i itemStatisticDo) FindInBatches(result *[]*model.ItemStatistic, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i itemStatisticDo) Attrs(attrs ...field.AssignExpr) IItemStatisticDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i itemStatisticDo) Assign(attrs ...field.AssignExpr) IItemStatisticDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i itemStatisticDo) Joins(fields ...field.RelationField) IItemStatisticDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i itemStatisticDo) Preload(fields ...field.RelationField) IItemStatisticDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i itemStatisticDo) FirstOrInit() (*model.ItemStatistic, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ItemStatistic), nil
	}
}

func (i itemStatisticDo) FirstOrCreate() (*model.ItemStatistic, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ItemStatistic), nil
	}
}

func (i itemStatisticDo) FindByPage(offset int, limit int) (result []*model.ItemStatistic, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i itemStatisticDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i itemStatisticDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i itemStatisticDo) Delete(models ...*model.ItemStatistic) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *itemStatisticDo) withDO(do gen.Dao) *itemStatisticDo {
	i.DO = *do.(*gen.DO)
	return i
}
