// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newTradeOrderItem(db *gorm.DB, opts ...gen.DOOption) tradeOrderItem {
	_tradeOrderItem := tradeOrderItem{}

	_tradeOrderItem.tradeOrderItemDo.UseDB(db, opts...)
	_tradeOrderItem.tradeOrderItemDo.UseModel(&model.TradeOrderItem{})

	tableName := _tradeOrderItem.tradeOrderItemDo.TableName()
	_tradeOrderItem.ALL = field.NewAsterisk(tableName)
	_tradeOrderItem.ID = field.NewInt64(tableName, "id")
	_tradeOrderItem.OrderID = field.NewInt64(tableName, "order_id")
	_tradeOrderItem.MallItemID = field.NewInt64(tableName, "mall_item_id")
	_tradeOrderItem.ItemID = field.NewString(tableName, "item_id")
	_tradeOrderItem.SkuID = field.NewString(tableName, "sku_id")
	_tradeOrderItem.SpuID = field.NewString(tableName, "spu_id")
	_tradeOrderItem.ItemIconURL = field.NewString(tableName, "item_icon_url")
	_tradeOrderItem.ItemSpecs = field.NewString(tableName, "item_specs")
	_tradeOrderItem.ItemName = field.NewString(tableName, "item_name")
	_tradeOrderItem.SalePrice = field.NewInt64(tableName, "sale_price")
	_tradeOrderItem.DiscountPrice = field.NewInt64(tableName, "discount_price")
	_tradeOrderItem.Quantity = field.NewInt32(tableName, "quantity")
	_tradeOrderItem.Subtotal = field.NewInt64(tableName, "subtotal")
	_tradeOrderItem.DiscountAmount = field.NewInt64(tableName, "discount_amount")
	_tradeOrderItem.PayAmount = field.NewInt64(tableName, "pay_amount")
	_tradeOrderItem.DeliveryStatus = field.NewInt32(tableName, "delivery_status")
	_tradeOrderItem.CreatedAt = field.NewTime(tableName, "created_at")
	_tradeOrderItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tradeOrderItem.IsDel = field.NewField(tableName, "is_del")

	_tradeOrderItem.fillFieldMap()

	return _tradeOrderItem
}

// tradeOrderItem 商城直购订单详情表
type tradeOrderItem struct {
	tradeOrderItemDo

	ALL            field.Asterisk
	ID             field.Int64  // 主键
	OrderID        field.Int64  // 订单ID
	MallItemID     field.Int64  // 直购商品ID
	ItemID         field.String // 商品ID
	SkuID          field.String // 商品 sku_id
	SpuID          field.String // 商品 spu_id
	ItemIconURL    field.String // 商品主图
	ItemSpecs      field.String // 商品属性
	ItemName       field.String // 商品名称
	SalePrice      field.Int64  // 售价(分)
	DiscountPrice  field.Int64  // 折扣价(分）
	Quantity       field.Int32  // 购买数量
	Subtotal       field.Int64  // 小计金额（= price * quantity）
	DiscountAmount field.Int64  // 商品享受的优惠金额
	PayAmount      field.Int64  // 实付金额
	DeliveryStatus field.Int32  // 发货状态
	CreatedAt      field.Time   // 创建时间
	UpdatedAt      field.Time   // 更新时间
	IsDel          field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (t tradeOrderItem) Table(newTableName string) *tradeOrderItem {
	t.tradeOrderItemDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tradeOrderItem) As(alias string) *tradeOrderItem {
	t.tradeOrderItemDo.DO = *(t.tradeOrderItemDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tradeOrderItem) updateTableName(table string) *tradeOrderItem {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt64(table, "id")
	t.OrderID = field.NewInt64(table, "order_id")
	t.MallItemID = field.NewInt64(table, "mall_item_id")
	t.ItemID = field.NewString(table, "item_id")
	t.SkuID = field.NewString(table, "sku_id")
	t.SpuID = field.NewString(table, "spu_id")
	t.ItemIconURL = field.NewString(table, "item_icon_url")
	t.ItemSpecs = field.NewString(table, "item_specs")
	t.ItemName = field.NewString(table, "item_name")
	t.SalePrice = field.NewInt64(table, "sale_price")
	t.DiscountPrice = field.NewInt64(table, "discount_price")
	t.Quantity = field.NewInt32(table, "quantity")
	t.Subtotal = field.NewInt64(table, "subtotal")
	t.DiscountAmount = field.NewInt64(table, "discount_amount")
	t.PayAmount = field.NewInt64(table, "pay_amount")
	t.DeliveryStatus = field.NewInt32(table, "delivery_status")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.IsDel = field.NewField(table, "is_del")

	t.fillFieldMap()

	return t
}

func (t *tradeOrderItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tradeOrderItem) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 19)
	t.fieldMap["id"] = t.ID
	t.fieldMap["order_id"] = t.OrderID
	t.fieldMap["mall_item_id"] = t.MallItemID
	t.fieldMap["item_id"] = t.ItemID
	t.fieldMap["sku_id"] = t.SkuID
	t.fieldMap["spu_id"] = t.SpuID
	t.fieldMap["item_icon_url"] = t.ItemIconURL
	t.fieldMap["item_specs"] = t.ItemSpecs
	t.fieldMap["item_name"] = t.ItemName
	t.fieldMap["sale_price"] = t.SalePrice
	t.fieldMap["discount_price"] = t.DiscountPrice
	t.fieldMap["quantity"] = t.Quantity
	t.fieldMap["subtotal"] = t.Subtotal
	t.fieldMap["discount_amount"] = t.DiscountAmount
	t.fieldMap["pay_amount"] = t.PayAmount
	t.fieldMap["delivery_status"] = t.DeliveryStatus
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["is_del"] = t.IsDel
}

func (t tradeOrderItem) clone(db *gorm.DB) tradeOrderItem {
	t.tradeOrderItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tradeOrderItem) replaceDB(db *gorm.DB) tradeOrderItem {
	t.tradeOrderItemDo.ReplaceDB(db)
	return t
}

type tradeOrderItemDo struct{ gen.DO }

type ITradeOrderItemDo interface {
	gen.SubQuery
	Debug() ITradeOrderItemDo
	WithContext(ctx context.Context) ITradeOrderItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITradeOrderItemDo
	WriteDB() ITradeOrderItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITradeOrderItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITradeOrderItemDo
	Not(conds ...gen.Condition) ITradeOrderItemDo
	Or(conds ...gen.Condition) ITradeOrderItemDo
	Select(conds ...field.Expr) ITradeOrderItemDo
	Where(conds ...gen.Condition) ITradeOrderItemDo
	Order(conds ...field.Expr) ITradeOrderItemDo
	Distinct(cols ...field.Expr) ITradeOrderItemDo
	Omit(cols ...field.Expr) ITradeOrderItemDo
	Join(table schema.Tabler, on ...field.Expr) ITradeOrderItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITradeOrderItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITradeOrderItemDo
	Group(cols ...field.Expr) ITradeOrderItemDo
	Having(conds ...gen.Condition) ITradeOrderItemDo
	Limit(limit int) ITradeOrderItemDo
	Offset(offset int) ITradeOrderItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITradeOrderItemDo
	Unscoped() ITradeOrderItemDo
	Create(values ...*model.TradeOrderItem) error
	CreateInBatches(values []*model.TradeOrderItem, batchSize int) error
	Save(values ...*model.TradeOrderItem) error
	First() (*model.TradeOrderItem, error)
	Take() (*model.TradeOrderItem, error)
	Last() (*model.TradeOrderItem, error)
	Find() ([]*model.TradeOrderItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradeOrderItem, err error)
	FindInBatches(result *[]*model.TradeOrderItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.TradeOrderItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITradeOrderItemDo
	Assign(attrs ...field.AssignExpr) ITradeOrderItemDo
	Joins(fields ...field.RelationField) ITradeOrderItemDo
	Preload(fields ...field.RelationField) ITradeOrderItemDo
	FirstOrInit() (*model.TradeOrderItem, error)
	FirstOrCreate() (*model.TradeOrderItem, error)
	FindByPage(offset int, limit int) (result []*model.TradeOrderItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITradeOrderItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t tradeOrderItemDo) Debug() ITradeOrderItemDo {
	return t.withDO(t.DO.Debug())
}

func (t tradeOrderItemDo) WithContext(ctx context.Context) ITradeOrderItemDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tradeOrderItemDo) ReadDB() ITradeOrderItemDo {
	return t.Clauses(dbresolver.Read)
}

func (t tradeOrderItemDo) WriteDB() ITradeOrderItemDo {
	return t.Clauses(dbresolver.Write)
}

func (t tradeOrderItemDo) Session(config *gorm.Session) ITradeOrderItemDo {
	return t.withDO(t.DO.Session(config))
}

func (t tradeOrderItemDo) Clauses(conds ...clause.Expression) ITradeOrderItemDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tradeOrderItemDo) Returning(value interface{}, columns ...string) ITradeOrderItemDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tradeOrderItemDo) Not(conds ...gen.Condition) ITradeOrderItemDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tradeOrderItemDo) Or(conds ...gen.Condition) ITradeOrderItemDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tradeOrderItemDo) Select(conds ...field.Expr) ITradeOrderItemDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tradeOrderItemDo) Where(conds ...gen.Condition) ITradeOrderItemDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tradeOrderItemDo) Order(conds ...field.Expr) ITradeOrderItemDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tradeOrderItemDo) Distinct(cols ...field.Expr) ITradeOrderItemDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tradeOrderItemDo) Omit(cols ...field.Expr) ITradeOrderItemDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tradeOrderItemDo) Join(table schema.Tabler, on ...field.Expr) ITradeOrderItemDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tradeOrderItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITradeOrderItemDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tradeOrderItemDo) RightJoin(table schema.Tabler, on ...field.Expr) ITradeOrderItemDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tradeOrderItemDo) Group(cols ...field.Expr) ITradeOrderItemDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tradeOrderItemDo) Having(conds ...gen.Condition) ITradeOrderItemDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tradeOrderItemDo) Limit(limit int) ITradeOrderItemDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tradeOrderItemDo) Offset(offset int) ITradeOrderItemDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tradeOrderItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITradeOrderItemDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tradeOrderItemDo) Unscoped() ITradeOrderItemDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tradeOrderItemDo) Create(values ...*model.TradeOrderItem) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tradeOrderItemDo) CreateInBatches(values []*model.TradeOrderItem, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tradeOrderItemDo) Save(values ...*model.TradeOrderItem) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tradeOrderItemDo) First() (*model.TradeOrderItem, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrderItem), nil
	}
}

func (t tradeOrderItemDo) Take() (*model.TradeOrderItem, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrderItem), nil
	}
}

func (t tradeOrderItemDo) Last() (*model.TradeOrderItem, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrderItem), nil
	}
}

func (t tradeOrderItemDo) Find() ([]*model.TradeOrderItem, error) {
	result, err := t.DO.Find()
	return result.([]*model.TradeOrderItem), err
}

func (t tradeOrderItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradeOrderItem, err error) {
	buf := make([]*model.TradeOrderItem, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tradeOrderItemDo) FindInBatches(result *[]*model.TradeOrderItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tradeOrderItemDo) Attrs(attrs ...field.AssignExpr) ITradeOrderItemDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tradeOrderItemDo) Assign(attrs ...field.AssignExpr) ITradeOrderItemDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tradeOrderItemDo) Joins(fields ...field.RelationField) ITradeOrderItemDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tradeOrderItemDo) Preload(fields ...field.RelationField) ITradeOrderItemDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tradeOrderItemDo) FirstOrInit() (*model.TradeOrderItem, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrderItem), nil
	}
}

func (t tradeOrderItemDo) FirstOrCreate() (*model.TradeOrderItem, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrderItem), nil
	}
}

func (t tradeOrderItemDo) FindByPage(offset int, limit int) (result []*model.TradeOrderItem, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tradeOrderItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tradeOrderItemDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tradeOrderItemDo) Delete(models ...*model.TradeOrderItem) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tradeOrderItemDo) withDO(do gen.Dao) *tradeOrderItemDo {
	t.DO = *do.(*gen.DO)
	return t
}
