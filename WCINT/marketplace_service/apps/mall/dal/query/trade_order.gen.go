// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newTradeOrder(db *gorm.DB, opts ...gen.DOOption) tradeOrder {
	_tradeOrder := tradeOrder{}

	_tradeOrder.tradeOrderDo.UseDB(db, opts...)
	_tradeOrder.tradeOrderDo.UseModel(&model.TradeOrder{})

	tableName := _tradeOrder.tradeOrderDo.TableName()
	_tradeOrder.ALL = field.NewAsterisk(tableName)
	_tradeOrder.ID = field.NewInt64(tableName, "id")
	_tradeOrder.UserID = field.NewString(tableName, "user_id")
	_tradeOrder.OrderType = field.NewInt32(tableName, "order_type")
	_tradeOrder.OrderStatus = field.NewInt32(tableName, "order_status")
	_tradeOrder.PaymentStatus = field.NewInt32(tableName, "payment_status")
	_tradeOrder.ShippingStatus = field.NewInt32(tableName, "shipping_status")
	_tradeOrder.TotalAmount = field.NewInt64(tableName, "total_amount")
	_tradeOrder.PayAmount = field.NewInt64(tableName, "pay_amount")
	_tradeOrder.FreightAmount = field.NewInt64(tableName, "freight_amount")
	_tradeOrder.DiscountAmount = field.NewInt64(tableName, "discount_amount")
	_tradeOrder.PaymentMethod = field.NewString(tableName, "payment_method")
	_tradeOrder.PaymentTime = field.NewTime(tableName, "payment_time")
	_tradeOrder.PayOrderID = field.NewString(tableName, "pay_order_id")
	_tradeOrder.AddressID = field.NewString(tableName, "address_id")
	_tradeOrder.ConsigneeName = field.NewString(tableName, "consignee_name")
	_tradeOrder.ConsigneePhone = field.NewString(tableName, "consignee_phone")
	_tradeOrder.ConsigneeAddress = field.NewField(tableName, "consignee_address")
	_tradeOrder.DeliveredAt = field.NewTime(tableName, "delivered_at")
	_tradeOrder.FinishedAt = field.NewTime(tableName, "finished_at")
	_tradeOrder.CancelType = field.NewInt32(tableName, "cancel_type")
	_tradeOrder.CancelAt = field.NewTime(tableName, "cancel_at")
	_tradeOrder.Terminal = field.NewString(tableName, "terminal")
	_tradeOrder.UserIP = field.NewString(tableName, "user_ip")
	_tradeOrder.UserRemark = field.NewString(tableName, "user_remark")
	_tradeOrder.SupplyChainOrderID = field.NewString(tableName, "supply_chain_order_id")
	_tradeOrder.IsUserDel = field.NewInt32(tableName, "is_user_del")
	_tradeOrder.CreatedAt = field.NewTime(tableName, "created_at")
	_tradeOrder.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tradeOrder.IsDel = field.NewField(tableName, "is_del")
	_tradeOrder.TradeOrderItem = tradeOrderHasManyTradeOrderItem{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("TradeOrderItem", "model.TradeOrderItem"),
	}

	_tradeOrder.fillFieldMap()

	return _tradeOrder
}

// tradeOrder 商城直购订单表
type tradeOrder struct {
	tradeOrderDo

	ALL                field.Asterisk
	ID                 field.Int64  // 主键
	UserID             field.String // 用户ID
	OrderType          field.Int32  // 订单类型（1=直购订单）
	OrderStatus        field.Int32  // 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
	PaymentStatus      field.Int32  // 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
	ShippingStatus     field.Int32  // 发货状态（0=未发货，1=已发货，2=已签收）
	TotalAmount        field.Int64  // 总金额
	PayAmount          field.Int64  // 支付金额
	FreightAmount      field.Int64  // 运费
	DiscountAmount     field.Int64  // 折扣金额
	PaymentMethod      field.String // 支付方式
	PaymentTime        field.Time   // 支付时间
	PayOrderID         field.String // 支付订单
	AddressID          field.String // 用户地址ID
	ConsigneeName      field.String // 收货人姓名
	ConsigneePhone     field.String // 收货人电话
	ConsigneeAddress   field.Field  // 收货地址
	DeliveredAt        field.Time   // 订单发货时间
	FinishedAt         field.Time   // 订单完成时间
	CancelType         field.Int32  // 取消类型（0=未取消，1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）
	CancelAt           field.Time   // 订单取消时间
	Terminal           field.String // 订单来源渠道
	UserIP             field.String // 用户 IP
	UserRemark         field.String // 用户备注
	SupplyChainOrderID field.String // 供应链订单ID
	IsUserDel          field.Int32  // 用户是否删除订单
	CreatedAt          field.Time   // 创建时间
	UpdatedAt          field.Time   // 更新时间
	IsDel              field.Field  // 是否删除【0->未删除; 1->删除】
	TradeOrderItem     tradeOrderHasManyTradeOrderItem

	fieldMap map[string]field.Expr
}

func (t tradeOrder) Table(newTableName string) *tradeOrder {
	t.tradeOrderDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tradeOrder) As(alias string) *tradeOrder {
	t.tradeOrderDo.DO = *(t.tradeOrderDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tradeOrder) updateTableName(table string) *tradeOrder {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt64(table, "id")
	t.UserID = field.NewString(table, "user_id")
	t.OrderType = field.NewInt32(table, "order_type")
	t.OrderStatus = field.NewInt32(table, "order_status")
	t.PaymentStatus = field.NewInt32(table, "payment_status")
	t.ShippingStatus = field.NewInt32(table, "shipping_status")
	t.TotalAmount = field.NewInt64(table, "total_amount")
	t.PayAmount = field.NewInt64(table, "pay_amount")
	t.FreightAmount = field.NewInt64(table, "freight_amount")
	t.DiscountAmount = field.NewInt64(table, "discount_amount")
	t.PaymentMethod = field.NewString(table, "payment_method")
	t.PaymentTime = field.NewTime(table, "payment_time")
	t.PayOrderID = field.NewString(table, "pay_order_id")
	t.AddressID = field.NewString(table, "address_id")
	t.ConsigneeName = field.NewString(table, "consignee_name")
	t.ConsigneePhone = field.NewString(table, "consignee_phone")
	t.ConsigneeAddress = field.NewField(table, "consignee_address")
	t.DeliveredAt = field.NewTime(table, "delivered_at")
	t.FinishedAt = field.NewTime(table, "finished_at")
	t.CancelType = field.NewInt32(table, "cancel_type")
	t.CancelAt = field.NewTime(table, "cancel_at")
	t.Terminal = field.NewString(table, "terminal")
	t.UserIP = field.NewString(table, "user_ip")
	t.UserRemark = field.NewString(table, "user_remark")
	t.SupplyChainOrderID = field.NewString(table, "supply_chain_order_id")
	t.IsUserDel = field.NewInt32(table, "is_user_del")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.IsDel = field.NewField(table, "is_del")

	t.fillFieldMap()

	return t
}

func (t *tradeOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tradeOrder) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 30)
	t.fieldMap["id"] = t.ID
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["order_type"] = t.OrderType
	t.fieldMap["order_status"] = t.OrderStatus
	t.fieldMap["payment_status"] = t.PaymentStatus
	t.fieldMap["shipping_status"] = t.ShippingStatus
	t.fieldMap["total_amount"] = t.TotalAmount
	t.fieldMap["pay_amount"] = t.PayAmount
	t.fieldMap["freight_amount"] = t.FreightAmount
	t.fieldMap["discount_amount"] = t.DiscountAmount
	t.fieldMap["payment_method"] = t.PaymentMethod
	t.fieldMap["payment_time"] = t.PaymentTime
	t.fieldMap["pay_order_id"] = t.PayOrderID
	t.fieldMap["address_id"] = t.AddressID
	t.fieldMap["consignee_name"] = t.ConsigneeName
	t.fieldMap["consignee_phone"] = t.ConsigneePhone
	t.fieldMap["consignee_address"] = t.ConsigneeAddress
	t.fieldMap["delivered_at"] = t.DeliveredAt
	t.fieldMap["finished_at"] = t.FinishedAt
	t.fieldMap["cancel_type"] = t.CancelType
	t.fieldMap["cancel_at"] = t.CancelAt
	t.fieldMap["terminal"] = t.Terminal
	t.fieldMap["user_ip"] = t.UserIP
	t.fieldMap["user_remark"] = t.UserRemark
	t.fieldMap["supply_chain_order_id"] = t.SupplyChainOrderID
	t.fieldMap["is_user_del"] = t.IsUserDel
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["is_del"] = t.IsDel

}

func (t tradeOrder) clone(db *gorm.DB) tradeOrder {
	t.tradeOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tradeOrder) replaceDB(db *gorm.DB) tradeOrder {
	t.tradeOrderDo.ReplaceDB(db)
	return t
}

type tradeOrderHasManyTradeOrderItem struct {
	db *gorm.DB

	field.RelationField
}

func (a tradeOrderHasManyTradeOrderItem) Where(conds ...field.Expr) *tradeOrderHasManyTradeOrderItem {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a tradeOrderHasManyTradeOrderItem) WithContext(ctx context.Context) *tradeOrderHasManyTradeOrderItem {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a tradeOrderHasManyTradeOrderItem) Session(session *gorm.Session) *tradeOrderHasManyTradeOrderItem {
	a.db = a.db.Session(session)
	return &a
}

func (a tradeOrderHasManyTradeOrderItem) Model(m *model.TradeOrder) *tradeOrderHasManyTradeOrderItemTx {
	return &tradeOrderHasManyTradeOrderItemTx{a.db.Model(m).Association(a.Name())}
}

type tradeOrderHasManyTradeOrderItemTx struct{ tx *gorm.Association }

func (a tradeOrderHasManyTradeOrderItemTx) Find() (result []*model.TradeOrderItem, err error) {
	return result, a.tx.Find(&result)
}

func (a tradeOrderHasManyTradeOrderItemTx) Append(values ...*model.TradeOrderItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a tradeOrderHasManyTradeOrderItemTx) Replace(values ...*model.TradeOrderItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a tradeOrderHasManyTradeOrderItemTx) Delete(values ...*model.TradeOrderItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a tradeOrderHasManyTradeOrderItemTx) Clear() error {
	return a.tx.Clear()
}

func (a tradeOrderHasManyTradeOrderItemTx) Count() int64 {
	return a.tx.Count()
}

type tradeOrderDo struct{ gen.DO }

type ITradeOrderDo interface {
	gen.SubQuery
	Debug() ITradeOrderDo
	WithContext(ctx context.Context) ITradeOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITradeOrderDo
	WriteDB() ITradeOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITradeOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITradeOrderDo
	Not(conds ...gen.Condition) ITradeOrderDo
	Or(conds ...gen.Condition) ITradeOrderDo
	Select(conds ...field.Expr) ITradeOrderDo
	Where(conds ...gen.Condition) ITradeOrderDo
	Order(conds ...field.Expr) ITradeOrderDo
	Distinct(cols ...field.Expr) ITradeOrderDo
	Omit(cols ...field.Expr) ITradeOrderDo
	Join(table schema.Tabler, on ...field.Expr) ITradeOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITradeOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITradeOrderDo
	Group(cols ...field.Expr) ITradeOrderDo
	Having(conds ...gen.Condition) ITradeOrderDo
	Limit(limit int) ITradeOrderDo
	Offset(offset int) ITradeOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITradeOrderDo
	Unscoped() ITradeOrderDo
	Create(values ...*model.TradeOrder) error
	CreateInBatches(values []*model.TradeOrder, batchSize int) error
	Save(values ...*model.TradeOrder) error
	First() (*model.TradeOrder, error)
	Take() (*model.TradeOrder, error)
	Last() (*model.TradeOrder, error)
	Find() ([]*model.TradeOrder, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradeOrder, err error)
	FindInBatches(result *[]*model.TradeOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.TradeOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITradeOrderDo
	Assign(attrs ...field.AssignExpr) ITradeOrderDo
	Joins(fields ...field.RelationField) ITradeOrderDo
	Preload(fields ...field.RelationField) ITradeOrderDo
	FirstOrInit() (*model.TradeOrder, error)
	FirstOrCreate() (*model.TradeOrder, error)
	FindByPage(offset int, limit int) (result []*model.TradeOrder, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITradeOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t tradeOrderDo) Debug() ITradeOrderDo {
	return t.withDO(t.DO.Debug())
}

func (t tradeOrderDo) WithContext(ctx context.Context) ITradeOrderDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tradeOrderDo) ReadDB() ITradeOrderDo {
	return t.Clauses(dbresolver.Read)
}

func (t tradeOrderDo) WriteDB() ITradeOrderDo {
	return t.Clauses(dbresolver.Write)
}

func (t tradeOrderDo) Session(config *gorm.Session) ITradeOrderDo {
	return t.withDO(t.DO.Session(config))
}

func (t tradeOrderDo) Clauses(conds ...clause.Expression) ITradeOrderDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tradeOrderDo) Returning(value interface{}, columns ...string) ITradeOrderDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tradeOrderDo) Not(conds ...gen.Condition) ITradeOrderDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tradeOrderDo) Or(conds ...gen.Condition) ITradeOrderDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tradeOrderDo) Select(conds ...field.Expr) ITradeOrderDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tradeOrderDo) Where(conds ...gen.Condition) ITradeOrderDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tradeOrderDo) Order(conds ...field.Expr) ITradeOrderDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tradeOrderDo) Distinct(cols ...field.Expr) ITradeOrderDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tradeOrderDo) Omit(cols ...field.Expr) ITradeOrderDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tradeOrderDo) Join(table schema.Tabler, on ...field.Expr) ITradeOrderDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tradeOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITradeOrderDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tradeOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) ITradeOrderDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tradeOrderDo) Group(cols ...field.Expr) ITradeOrderDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tradeOrderDo) Having(conds ...gen.Condition) ITradeOrderDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tradeOrderDo) Limit(limit int) ITradeOrderDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tradeOrderDo) Offset(offset int) ITradeOrderDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tradeOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITradeOrderDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tradeOrderDo) Unscoped() ITradeOrderDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tradeOrderDo) Create(values ...*model.TradeOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tradeOrderDo) CreateInBatches(values []*model.TradeOrder, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tradeOrderDo) Save(values ...*model.TradeOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tradeOrderDo) First() (*model.TradeOrder, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrder), nil
	}
}

func (t tradeOrderDo) Take() (*model.TradeOrder, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrder), nil
	}
}

func (t tradeOrderDo) Last() (*model.TradeOrder, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrder), nil
	}
}

func (t tradeOrderDo) Find() ([]*model.TradeOrder, error) {
	result, err := t.DO.Find()
	return result.([]*model.TradeOrder), err
}

func (t tradeOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradeOrder, err error) {
	buf := make([]*model.TradeOrder, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tradeOrderDo) FindInBatches(result *[]*model.TradeOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tradeOrderDo) Attrs(attrs ...field.AssignExpr) ITradeOrderDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tradeOrderDo) Assign(attrs ...field.AssignExpr) ITradeOrderDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tradeOrderDo) Joins(fields ...field.RelationField) ITradeOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tradeOrderDo) Preload(fields ...field.RelationField) ITradeOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tradeOrderDo) FirstOrInit() (*model.TradeOrder, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrder), nil
	}
}

func (t tradeOrderDo) FirstOrCreate() (*model.TradeOrder, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrder), nil
	}
}

func (t tradeOrderDo) FindByPage(offset int, limit int) (result []*model.TradeOrder, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tradeOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tradeOrderDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tradeOrderDo) Delete(models ...*model.TradeOrder) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tradeOrderDo) withDO(do gen.Dao) *tradeOrderDo {
	t.DO = *do.(*gen.DO)
	return t
}
