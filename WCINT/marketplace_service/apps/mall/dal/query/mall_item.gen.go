// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newMallItem(db *gorm.DB, opts ...gen.DOOption) mallItem {
	_mallItem := mallItem{}

	_mallItem.mallItemDo.UseDB(db, opts...)
	_mallItem.mallItemDo.UseModel(&model.MallItem{})

	tableName := _mallItem.mallItemDo.TableName()
	_mallItem.ALL = field.NewAsterisk(tableName)
	_mallItem.ID = field.NewInt64(tableName, "id")
	_mallItem.Status = field.NewInt32(tableName, "status")
	_mallItem.ItemID = field.NewString(tableName, "item_id")
	_mallItem.ItemName = field.NewString(tableName, "item_name")
	_mallItem.SkuID = field.NewString(tableName, "sku_id")
	_mallItem.SpuID = field.NewString(tableName, "spu_id")
	_mallItem.IPID = field.NewString(tableName, "ip_id")
	_mallItem.TrademarkID = field.NewString(tableName, "trademark_id")
	_mallItem.CategoryID = field.NewString(tableName, "category_id")
	_mallItem.ItemSpecs = field.NewString(tableName, "item_specs")
	_mallItem.ItemIconURL = field.NewString(tableName, "item_icon_url")
	_mallItem.SalePrice = field.NewInt64(tableName, "sale_price")
	_mallItem.DiscountPrice = field.NewInt64(tableName, "discount_price")
	_mallItem.Discount = field.NewInt32(tableName, "discount")
	_mallItem.Freight = field.NewInt64(tableName, "freight")
	_mallItem.StockType = field.NewInt32(tableName, "stock_type")
	_mallItem.Stock = field.NewInt32(tableName, "stock")
	_mallItem.AvailableStock = field.NewInt32(tableName, "available_stock")
	_mallItem.TotalLimit = field.NewInt32(tableName, "total_limit")
	_mallItem.DailyLimit = field.NewInt32(tableName, "daily_limit")
	_mallItem.StartTime = field.NewTime(tableName, "start_time")
	_mallItem.Priority = field.NewInt32(tableName, "priority")
	_mallItem.SalesVolume = field.NewInt32(tableName, "sales_volume")
	_mallItem.WishlistCount = field.NewInt32(tableName, "wishlist_count")
	_mallItem.ShelfTime = field.NewTime(tableName, "shelf_time")
	_mallItem.CreatedBy = field.NewString(tableName, "created_by")
	_mallItem.CreatedAt = field.NewTime(tableName, "created_at")
	_mallItem.UpdatedBy = field.NewString(tableName, "updated_by")
	_mallItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_mallItem.IsDel = field.NewField(tableName, "is_del")

	_mallItem.fillFieldMap()

	return _mallItem
}

// mallItem 商城直购商品表
type mallItem struct {
	mallItemDo

	ALL            field.Asterisk
	ID             field.Int64  // 主键
	Status         field.Int32  // 状态（0=待上架，1=已上架，2=已下架）
	ItemID         field.String // 商品 id
	ItemName       field.String // 商品名称
	SkuID          field.String // 商品 sku_id
	SpuID          field.String // 商品 spu_id
	IPID           field.String // 商品 ip_id
	TrademarkID    field.String // 商品品牌id
	CategoryID     field.String // 商品分类id
	ItemSpecs      field.String // 商品属性
	ItemIconURL    field.String // 商品主图
	SalePrice      field.Int64  // 售价(分)
	DiscountPrice  field.Int64  // 折扣价(分）
	Discount       field.Int32  // 折扣
	Freight        field.Int64  // 运费(分)
	StockType      field.Int32  // 库存类型（1=限定库存，2=同步商品库存）
	Stock          field.Int32  // 限定库存量
	AvailableStock field.Int32  // 可用库存
	TotalLimit     field.Int32  // 总限购数量
	DailyLimit     field.Int32  // 每日限购数量
	StartTime      field.Time   // 开始展示时间
	Priority       field.Int32  // 优先级
	SalesVolume    field.Int32  // 销量
	WishlistCount  field.Int32  // 想要数量
	ShelfTime      field.Time   // 上架时间
	CreatedBy      field.String // 创建人
	CreatedAt      field.Time   // 创建时间
	UpdatedBy      field.String // 更新人
	UpdatedAt      field.Time   // 更新时间
	IsDel          field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (m mallItem) Table(newTableName string) *mallItem {
	m.mallItemDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mallItem) As(alias string) *mallItem {
	m.mallItemDo.DO = *(m.mallItemDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mallItem) updateTableName(table string) *mallItem {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.Status = field.NewInt32(table, "status")
	m.ItemID = field.NewString(table, "item_id")
	m.ItemName = field.NewString(table, "item_name")
	m.SkuID = field.NewString(table, "sku_id")
	m.SpuID = field.NewString(table, "spu_id")
	m.IPID = field.NewString(table, "ip_id")
	m.TrademarkID = field.NewString(table, "trademark_id")
	m.CategoryID = field.NewString(table, "category_id")
	m.ItemSpecs = field.NewString(table, "item_specs")
	m.ItemIconURL = field.NewString(table, "item_icon_url")
	m.SalePrice = field.NewInt64(table, "sale_price")
	m.DiscountPrice = field.NewInt64(table, "discount_price")
	m.Discount = field.NewInt32(table, "discount")
	m.Freight = field.NewInt64(table, "freight")
	m.StockType = field.NewInt32(table, "stock_type")
	m.Stock = field.NewInt32(table, "stock")
	m.AvailableStock = field.NewInt32(table, "available_stock")
	m.TotalLimit = field.NewInt32(table, "total_limit")
	m.DailyLimit = field.NewInt32(table, "daily_limit")
	m.StartTime = field.NewTime(table, "start_time")
	m.Priority = field.NewInt32(table, "priority")
	m.SalesVolume = field.NewInt32(table, "sales_volume")
	m.WishlistCount = field.NewInt32(table, "wishlist_count")
	m.ShelfTime = field.NewTime(table, "shelf_time")
	m.CreatedBy = field.NewString(table, "created_by")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedBy = field.NewString(table, "updated_by")
	m.UpdatedAt = field.NewTime(table, "updated_at")
	m.IsDel = field.NewField(table, "is_del")

	m.fillFieldMap()

	return m
}

func (m *mallItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mallItem) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 30)
	m.fieldMap["id"] = m.ID
	m.fieldMap["status"] = m.Status
	m.fieldMap["item_id"] = m.ItemID
	m.fieldMap["item_name"] = m.ItemName
	m.fieldMap["sku_id"] = m.SkuID
	m.fieldMap["spu_id"] = m.SpuID
	m.fieldMap["ip_id"] = m.IPID
	m.fieldMap["trademark_id"] = m.TrademarkID
	m.fieldMap["category_id"] = m.CategoryID
	m.fieldMap["item_specs"] = m.ItemSpecs
	m.fieldMap["item_icon_url"] = m.ItemIconURL
	m.fieldMap["sale_price"] = m.SalePrice
	m.fieldMap["discount_price"] = m.DiscountPrice
	m.fieldMap["discount"] = m.Discount
	m.fieldMap["freight"] = m.Freight
	m.fieldMap["stock_type"] = m.StockType
	m.fieldMap["stock"] = m.Stock
	m.fieldMap["available_stock"] = m.AvailableStock
	m.fieldMap["total_limit"] = m.TotalLimit
	m.fieldMap["daily_limit"] = m.DailyLimit
	m.fieldMap["start_time"] = m.StartTime
	m.fieldMap["priority"] = m.Priority
	m.fieldMap["sales_volume"] = m.SalesVolume
	m.fieldMap["wishlist_count"] = m.WishlistCount
	m.fieldMap["shelf_time"] = m.ShelfTime
	m.fieldMap["created_by"] = m.CreatedBy
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_by"] = m.UpdatedBy
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_del"] = m.IsDel
}

func (m mallItem) clone(db *gorm.DB) mallItem {
	m.mallItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mallItem) replaceDB(db *gorm.DB) mallItem {
	m.mallItemDo.ReplaceDB(db)
	return m
}

type mallItemDo struct{ gen.DO }

type IMallItemDo interface {
	gen.SubQuery
	Debug() IMallItemDo
	WithContext(ctx context.Context) IMallItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMallItemDo
	WriteDB() IMallItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMallItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMallItemDo
	Not(conds ...gen.Condition) IMallItemDo
	Or(conds ...gen.Condition) IMallItemDo
	Select(conds ...field.Expr) IMallItemDo
	Where(conds ...gen.Condition) IMallItemDo
	Order(conds ...field.Expr) IMallItemDo
	Distinct(cols ...field.Expr) IMallItemDo
	Omit(cols ...field.Expr) IMallItemDo
	Join(table schema.Tabler, on ...field.Expr) IMallItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMallItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMallItemDo
	Group(cols ...field.Expr) IMallItemDo
	Having(conds ...gen.Condition) IMallItemDo
	Limit(limit int) IMallItemDo
	Offset(offset int) IMallItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMallItemDo
	Unscoped() IMallItemDo
	Create(values ...*model.MallItem) error
	CreateInBatches(values []*model.MallItem, batchSize int) error
	Save(values ...*model.MallItem) error
	First() (*model.MallItem, error)
	Take() (*model.MallItem, error)
	Last() (*model.MallItem, error)
	Find() ([]*model.MallItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MallItem, err error)
	FindInBatches(result *[]*model.MallItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MallItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMallItemDo
	Assign(attrs ...field.AssignExpr) IMallItemDo
	Joins(fields ...field.RelationField) IMallItemDo
	Preload(fields ...field.RelationField) IMallItemDo
	FirstOrInit() (*model.MallItem, error)
	FirstOrCreate() (*model.MallItem, error)
	FindByPage(offset int, limit int) (result []*model.MallItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMallItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mallItemDo) Debug() IMallItemDo {
	return m.withDO(m.DO.Debug())
}

func (m mallItemDo) WithContext(ctx context.Context) IMallItemDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mallItemDo) ReadDB() IMallItemDo {
	return m.Clauses(dbresolver.Read)
}

func (m mallItemDo) WriteDB() IMallItemDo {
	return m.Clauses(dbresolver.Write)
}

func (m mallItemDo) Session(config *gorm.Session) IMallItemDo {
	return m.withDO(m.DO.Session(config))
}

func (m mallItemDo) Clauses(conds ...clause.Expression) IMallItemDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mallItemDo) Returning(value interface{}, columns ...string) IMallItemDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mallItemDo) Not(conds ...gen.Condition) IMallItemDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mallItemDo) Or(conds ...gen.Condition) IMallItemDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mallItemDo) Select(conds ...field.Expr) IMallItemDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mallItemDo) Where(conds ...gen.Condition) IMallItemDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mallItemDo) Order(conds ...field.Expr) IMallItemDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mallItemDo) Distinct(cols ...field.Expr) IMallItemDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mallItemDo) Omit(cols ...field.Expr) IMallItemDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mallItemDo) Join(table schema.Tabler, on ...field.Expr) IMallItemDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mallItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMallItemDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mallItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IMallItemDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mallItemDo) Group(cols ...field.Expr) IMallItemDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mallItemDo) Having(conds ...gen.Condition) IMallItemDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mallItemDo) Limit(limit int) IMallItemDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mallItemDo) Offset(offset int) IMallItemDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mallItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMallItemDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mallItemDo) Unscoped() IMallItemDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mallItemDo) Create(values ...*model.MallItem) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mallItemDo) CreateInBatches(values []*model.MallItem, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mallItemDo) Save(values ...*model.MallItem) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mallItemDo) First() (*model.MallItem, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MallItem), nil
	}
}

func (m mallItemDo) Take() (*model.MallItem, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MallItem), nil
	}
}

func (m mallItemDo) Last() (*model.MallItem, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MallItem), nil
	}
}

func (m mallItemDo) Find() ([]*model.MallItem, error) {
	result, err := m.DO.Find()
	return result.([]*model.MallItem), err
}

func (m mallItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MallItem, err error) {
	buf := make([]*model.MallItem, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mallItemDo) FindInBatches(result *[]*model.MallItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mallItemDo) Attrs(attrs ...field.AssignExpr) IMallItemDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mallItemDo) Assign(attrs ...field.AssignExpr) IMallItemDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mallItemDo) Joins(fields ...field.RelationField) IMallItemDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mallItemDo) Preload(fields ...field.RelationField) IMallItemDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mallItemDo) FirstOrInit() (*model.MallItem, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MallItem), nil
	}
}

func (m mallItemDo) FirstOrCreate() (*model.MallItem, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MallItem), nil
	}
}

func (m mallItemDo) FindByPage(offset int, limit int) (result []*model.MallItem, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mallItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mallItemDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mallItemDo) Delete(models ...*model.MallItem) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mallItemDo) withDO(do gen.Dao) *mallItemDo {
	m.DO = *do.(*gen.DO)
	return m
}
