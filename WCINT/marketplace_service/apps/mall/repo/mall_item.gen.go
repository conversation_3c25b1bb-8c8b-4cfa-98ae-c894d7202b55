package repo

import (
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/dal/query"
	"marketplace_service/pkg/pagination"
	"marketplace_service/pkg/search"
)

type MallItemRepository struct {
	do query.IMallItemDo
}

func NewMallItemRepo(do query.IMallItemDo) *MallItemRepository {
	return &MallItemRepository{
		do: do,
	}
}

func (r *MallItemRepository) SelectOne(wrapper *search.Wrapper) (*model.MallItem, error) {
	records, err := r.do.Scopes(wrapper.Build()).Find()
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	if len(records) > 1 {
		return nil, errors.New("more than one item found")
	}
	return records[0], nil
}

func (r *MallItemRepository) SelectList(wrapper *search.Wrapper) ([]*model.MallItem, error) {
	records, err := r.do.Scopes(wrapper.Build()).Find()
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (r *MallItemRepository) SelectPage(wrapper *search.Wrapper, req pagination.IPagination) ([]*model.MallItem, int64, error) {
	records, count, err := r.do.Scopes(wrapper.Build()).
		FindByPage(search.Paginate(req.GetPageSize(), req.GetPage()))
	if err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (r *MallItemRepository) QuickSelectPage(req pagination.IPagination) ([]*model.MallItem, int64, error) {
	records, count, err := r.do.Scopes(search.MakeCondition(req)).
		FindByPage(search.Paginate(req.GetPageSize(), req.GetPage()))
	if err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (r *MallItemRepository) Count(wrapper *search.Wrapper) (int64, error) {
	count, err := r.do.Scopes(wrapper.Build()).Count()
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (r *MallItemRepository) Save(model *model.MallItem) error {
	err := r.do.Create(model)
	if err != nil {
		return err
	}
	return nil
}

func (r *MallItemRepository) BatchSave(models []*model.MallItem, batchSize int) error {
	err := r.do.CreateInBatches(models, batchSize)
	if err != nil {
		return err
	}
	return nil
}

func (r *MallItemRepository) UpdateById(model *model.MallItem) error {
	result, err := r.do.Updates(model)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *MallItemRepository) Update(ms *model.MallItem, wrapper *search.Wrapper) error {
	if wrapper != nil {
		r.do = r.do.Scopes(
			wrapper.Build(),
		)
	}
	result, err := r.do.Updates(ms)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *MallItemRepository) UpdateField(params interface{}, wrapper *search.Wrapper) error {
	if wrapper != nil {
		r.do = r.do.Scopes(
			wrapper.Build(),
		)
	}
	result, err := r.do.Updates(params)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *MallItemRepository) RemoveByIds(ms ...*model.MallItem) error {
	result, err := r.do.Delete(ms...)
	if err != nil {
		return err
	} else if result.RowsAffected != int64(len(ms)) {
		return UpdateFail
	}
	return nil
}
