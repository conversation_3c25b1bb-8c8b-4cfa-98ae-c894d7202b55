package web

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetWebTradeOrderList
// @Summary 查询直购订单列表
// @Description 查询直购订单列表
// @Tags 用户端-订单管理
// @Param data query define.GetWebTradeOrderListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebTradeOrderListResp}
// @Router  /web/v1/trade_order/list [get]
// @Security Bearer
func GetWebTradeOrderList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebTradeOrderListReq{}, s.GetWebTradeOrderList)
}

// GetWebTradeOrderDetail
// @Summary 查询直购订单详情
// @Description 查询直购订单详情
// @Tags 用户端-订单管理
// @Param data query define.GetWebTradeOrderDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.GetWebTradeOrderDetailResp}
// @Router /web/v1/trade_order/detail [get]
// @Security Bearer
func GetWebTradeOrderDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebTradeOrderDetailReq{}, s.GetWebTradeOrderDetail)
}

// GetWebTradeOrderStatusStat
// @Summary 查询直购订单状态统计
// @Description 查询直购订单状态统计
// @Tags 用户端-订单管理
// @Param data query define.GetWebTradeOrderStatusStatReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.GetWebTradeOrderStatusStatResp}
// @Router /web/v1/trade_order/status_stat [get]
// @Security Bearer
func GetWebTradeOrderStatusStat(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebTradeOrderStatusStatReq{}, s.GetWebTradeOrderStatusStat)
}

// CancelTradeOrder
// @Summary 取消订单
// @Description 取消订单
// @Tags 用户端-订单管理
// @Param data body define.CancelTradeOrderReq true "请求参数"
// @Success 200 {object} response.Data{data=define.CancelTradeOrderResp}
// @Router /web/v1/trade_order/cancel [post]
// @Security Bearer
func CancelTradeOrder(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.CancelTradeOrderReq{}, s.CancelTradeOrder)
}

// UpdateTradeOrderAddress
// @Summary 修改地址
// @Description 修改地址
// @Tags 用户端-订单管理
// @Param data body define.UpdateTradeOrderAddressReq true "请求参数"
// @Success 200 {object} response.Data{data=define.UpdateTradeOrderAddressResp}
// @Router /web/v1/trade_order/update_address [post]
// @Security Bearer
func UpdateTradeOrderAddress(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.UpdateTradeOrderAddressReq{}, s.UpdateTradeOrderAddress)
}

// DelTradeOrder
// @Summary 删除订单
// @Description 删除订单
// @Tags 用户端-订单管理
// @Param data body define.DelTradeOrderReq true "请求参数"
// @Success 200 {object} response.Data{data=define.DelTradeOrderResp}
// @Router /web/v1/trade_order/del [post]
// @Security Bearer
func DelTradeOrder(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.DelTradeOrderReq{}, s.DelTradeOrder)
}

// ConfirmTradeOrder
// @Summary 确定收货
// @Description 确定收货
// @Tags 用户端-订单管理
// @Param data body define.ConfirmTradeOrderReq true "请求参数"
// @Success 200 {object} response.Data{data=define.ConfirmTradeOrderResp}
// @Router /web/v1/trade_order/confirm [post]
// @Security Bearer
func ConfirmTradeOrder(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ConfirmTradeOrderReq{}, s.ConfirmTradeOrder)
}

// GetWebTradeOrderFreightInfo
// @Summary 查询直购订单物流信息
// @Description 查询直购订单物流信息
// @Tags 用户端-订单管理
// @Param data query define.GetWebTradeOrderFreightInfoReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.GetWebTradeOrderFreightInfoResp}
// @Router /web/v1/trade_order/freight_info [get]
// @Security Bearer
func GetWebTradeOrderFreightInfo(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebTradeOrderFreightInfoReq{}, s.GetWebTradeOrderFreightInfo)
}
