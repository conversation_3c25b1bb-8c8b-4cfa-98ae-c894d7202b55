package service

import (
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/logic"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils/snowflakeutl"
)

// GetWishlistList 想要列表
func (s *Service) GetWishlistList(req *define.WebWishlistListReq) (*define.WebWishlistListResp, error) {
	userID := s.GetUserId()
	if userID == "" {
		return nil, errors.New("用户未登录")
	}
	offset, pageSize := search.Paginate(req.GetPageSize(), req.GetPage())
	uw := repo.GetQuery().UserWishlist
	wrapper := search.NewWrapper().Where(uw.UserID.Eq(userID), uw.Status.Eq(enums.UserWishlistStatusWant.Val())).OrderBy(uw.UpdatedAt.Desc())
	userWishlists, err := repo.NewUserWishlistRepo(uw.WithContext(s.ctx).Offset(offset).Limit(pageSize)).SelectList(wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "获取想要列表失败")
	}
	// 提取商品ID
	itemIDs := make([]string, 0, len(userWishlists))
	for _, v := range userWishlists {
		itemIDs = append(itemIDs, v.ItemID)
	}
	// 获取上架的或者售中的商品
	mallItems, err := logic.GetLatestItemsWithSQL(itemIDs)
	if err != nil {
		return nil, errors.Wrap(err, "获取商品列表失败")
	}
	mallItemsMap := make(map[string]*model.MallItem, len(mallItems))
	for _, v := range mallItems {
		mallItemsMap[v.ItemID] = v
	}
	list := make([]*define.WebWishlistListData, 0, len(userWishlists))
	for _, v := range userWishlists {
		mallItem := mallItemsMap[v.ItemID]
		list = append(list, &define.WebWishlistListData{
			ID:            v.ID,
			MallItemID:    mallItem.ID,
			ItemID:        mallItem.ItemID,
			ItemName:      mallItem.ItemName,
			IconUrl:       mallItem.ItemIconURL,
			SalePrice:     mallItem.SalePrice,
			DiscountPrice: logic.CalculateDiscountedPrice(mallItem.SalePrice, mallItem.Discount),
			Discount:      mallItem.Discount,
		})
	}

	return &define.WebWishlistListResp{
		HasMore: len(list) >= req.PageSize,
		List:    list,
	}, nil
}

// AddToWishlist 添加到想要
func (s *Service) AddToWishlist(req *define.WebWishlistReq) (any, error) {
	userID := s.GetUserId()
	if userID == "" {
		return nil, errors.New("用户未登录")
	}
	mi := repo.GetQuery().MallItem
	wrapper := search.NewWrapper().Where(mi.ItemID.Eq(req.ItemID)).OrderBy(mi.CreatedAt.Desc())
	mallItem, err := repo.NewMallItemRepo(mi.WithContext(s.ctx).Limit(1)).SelectOne(wrapper)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.Wrap(err, "直购商品不存在")
	}
	userWishlist, err := logic.IsWished(s.ctx, userID, req.ItemID)
	if err != nil {
		return nil, errors.Wrap(err, "检查是否已加入想要失败")
	}
	if userWishlist != nil {
		log.Ctx(s.ctx).Warnf("商品 [%s] 已存在于用户 [%s] 的想要中", req.ItemID, userID)
		return response.Empty{}, nil
	}
	updateUserWishlist := &model.UserWishlist{
		ID:     snowflakeutl.GenerateID(),
		UserID: userID,
		ItemID: req.ItemID,
		SkuID:  mallItem.SkuID,
		Status: enums.UserWishlistStatusWant.Val(),
	}
	uw := repo.GetQuery().UserWishlist
	if err := repo.NewUserWishlistRepo(uw.WithContext(s.ctx).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "user_id"}, {Name: "item_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"status"}),
		}),
	).Save(updateUserWishlist); err != nil {
		return nil, errors.Wrap(err, "加入想要失败")
	}
	// 更新统计数据
	is := repo.Query(s.ctx).ItemStatistic
	updateWrapper := search.NewWrapper().Where(
		is.ItemID.Eq(req.ItemID),
		is.SkuID.Eq(mallItem.SkuID),
	)

	if err := repo.NewItemStatisticRepo(is.WithContext(s.ctx)).
		UpdateField(map[string]interface{}{
			"WishlistCount": gorm.Expr("wishlist_count + ?", 1),
		}, updateWrapper); err != nil {
		// 如果记录不存在，则创建新记录
		if errors.Is(err, repo.UpdateFail) {
			newStat := &model.ItemStatistic{
				ID:            snowflakeutl.GenerateID(),
				ItemID:        req.ItemID,
				SkuID:         mallItem.SkuID,
				WishlistCount: 1,
			}
			if createErr := repo.NewItemStatisticRepo(is.WithContext(s.ctx)).Save(newStat); createErr != nil {
				return nil, errors.Wrap(createErr, "创建商品统计信息失败")
			}
		} else {
			return nil, errors.Wrap(err, "更新商品统计信息失败")
		}
	}
	return response.Empty{}, nil
}

// RemoveFromWishlist 从想要移除
func (s *Service) RemoveFromWishlist(req *define.WebWishlistReq) (any, error) {
	userID := s.GetUserId()
	if userID == "" {
		return nil, errors.New("用户未登录")
	}
	uw := repo.GetQuery().UserWishlist
	wrapper := search.NewWrapper().Where(uw.UserID.Eq(userID), uw.ItemID.Eq(req.ItemID))
	userWishlist, err := repo.NewUserWishlistRepo(uw.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.Wrap(err, "检查是否已加入想要失败")
	}
	if userWishlist == nil || userWishlist.Status == enums.UserWishlistStatusCancel.Val() {
		log.Ctx(s.ctx).Warnf("商品 [%s] 已不在用户 [%s] 的想要中", req.ItemID, userID)
		return response.Empty{}, nil
	}
	if err = repo.NewUserWishlistRepo(uw.WithContext(s.ctx)).
		UpdateField(map[string]interface{}{"status": enums.UserWishlistStatusCancel.Val()}, wrapper); err != nil {
		return nil, errors.Wrap(err, "从想要移除失败")
	}
	is := repo.Query(s.ctx).ItemStatistic
	updateWrapper := search.NewWrapper().Where(
		is.ItemID.Eq(req.ItemID),
		is.SkuID.Eq(userWishlist.SkuID),
		is.WishlistCount.Gte(1),
	)
	if err := repo.NewItemStatisticRepo(is.WithContext(s.ctx)).
		UpdateField(map[string]interface{}{"WishlistCount": gorm.Expr("wishlist_count - ?", 1)}, updateWrapper); err != nil {
		return nil, errors.Wrap(err, "更新商品统计信息失败")
	}
	return response.Empty{}, nil
}
