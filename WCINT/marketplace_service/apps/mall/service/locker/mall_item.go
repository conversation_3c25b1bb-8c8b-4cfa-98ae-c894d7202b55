package locker

import (
	"fmt"
	"time"
)

// MallItemAction 直购商品
type MallItemAction string

const (
	UserBuy MallItemAction = "user_buy" // 购买商品
)

type MallItemLock struct {
	ac  MallItemAction // 行为
	tag string         // 唯一标识
}

func (p *MallItemLock) GetCacheKey() string {
	return fmt.Sprintf("marketplace_service:mall_item:locker:%s:%s", p.ac, p.tag)
}

func (p *MallItemLock) LockTime() time.Duration {
	return time.Second * 10
}

func NewMallItemActionLock(ac MallItemAction, tag string) *MallItemLock {
	return &MallItemLock{ac: ac, tag: tag}
}
