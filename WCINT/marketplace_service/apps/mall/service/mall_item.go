package service

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"fmt"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/locker"
	"marketplace_service/apps/mall/service/logic"
	"marketplace_service/global"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/pkg/utils/snowflakeutl"
	"strconv"
	"time"
)

// GetMallItemList 获取商品分页列表
func (s *Service) GetMallItemList(req *define.MallItemPageReq) (*define.MallItemPageResp, error) {
	schema := repo.GetQuery().MallItem
	mallItems, count, err := repo.NewMallItemRepo(schema.WithContext(s.ctx).Order(schema.CreatedAt.Desc())).QuickSelectPage(req)
	if err != nil {
		return nil, errors.Wrap(err, "查询商品列表失败")
	}
	if len(mallItems) == 0 {
		return &define.MallItemPageResp{
			List:  []*define.MallItemPageData{},
			Total: count,
		}, nil
	}
	itemIds := make([]string, len(mallItems))
	ipIds := make([]string, len(mallItems))
	categoryIds := make([]string, len(mallItems))
	trademarkIds := make([]string, len(mallItems))
	for i, item := range mallItems {
		itemIds[i] = item.ItemID
		ipIds[i] = item.IPID
		categoryIds[i] = item.CategoryID
		trademarkIds[i] = item.TrademarkID
	}
	itemInfoMap, _ := logic.GetItemInfoMap(s.ctx, itemIds...)
	ipInfoMap, _ := logic.GetIpInfoMap(s.ctx, ipIds...)
	categoryInfoMap, _ := logic.GetItemClassifyInfoMap(s.ctx, categoryIds...)
	trademarkInfoMap, _ := logic.GetTrademarksInfoMap(s.ctx, trademarkIds...)

	mallItemList := make([]*define.MallItemPageData, 0, len(mallItems))
	for _, mallItem := range mallItems {
		data := &define.MallItemPageData{
			ID:             mallItem.ID,
			Status:         mallItem.Status,
			ItemID:         mallItem.ItemID,
			SkuID:          mallItem.SkuID,
			ItemName:       mallItem.ItemName,
			ItemIconURL:    mallItem.ItemIconURL,
			ItemSpecs:      mallItem.ItemSpecs,
			SpuID:          mallItem.SpuID,
			IPID:           mallItem.IPID,
			CategoryID:     mallItem.CategoryID,
			TrademarkID:    mallItem.TrademarkID,
			Discount:       mallItem.Discount,
			DiscountPrice:  logic.CalculateDiscountedPrice(mallItem.SalePrice, mallItem.Discount),
			Stock:          mallItem.Stock,
			AvailableStock: mallItem.AvailableStock,
			SalePrice:      mallItem.SalePrice,
			Freight:        mallItem.Freight,
			StartTime:      mallItem.StartTime,
			Priority:       mallItem.Priority,
			UpdatedBy:      mallItem.UpdatedBy,
		}
		if itemInfo, ok := itemInfoMap[mallItem.ItemID]; ok {
			data.PurchasePrice = itemInfo.PurchasePrice
		}
		if ipInfo, ok := ipInfoMap[mallItem.IPID]; ok {
			data.IPName = ipInfo.Name
		}
		if categoryInfo, ok := categoryInfoMap[mallItem.CategoryID]; ok {
			data.CategoryName = categoryInfo.Name
		}
		if trademarkInfo, ok := trademarkInfoMap[mallItem.TrademarkID]; ok {
			data.TrademarkName = trademarkInfo.Name
		}
		mallItemList = append(mallItemList, data)
	}
	return &define.MallItemPageResp{
		List:  mallItemList,
		Total: count,
	}, nil
}

// GetMallItemDetail 获取商品详情
func (s *Service) GetMallItemDetail(req *define.MallItemDetailReq) (*define.MallItemDetailResp, error) {
	schema := repo.GetQuery().MallItem
	wrapper := search.NewWrapper().Where(schema.ID.Eq(req.ID))
	mallItem, err := repo.NewMallItemRepo(schema.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "查询商品详情失败")
	}
	itemInfo, err := logic.GetItemInfoByRemote(s.ctx, mallItem.ItemID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询商品信息失败: %v", err)
		return nil, errors.Errorf("查询商品信息失败，ItemID:%s", mallItem.ItemID)
	}
	return &define.MallItemDetailResp{
		ID:             mallItem.ID,
		Status:         mallItem.Status,
		ItemID:         mallItem.ItemID,
		ItemName:       mallItem.ItemName,
		SkuID:          mallItem.SkuID,
		SpuID:          mallItem.SpuID,
		TrademarkID:    mallItem.TrademarkID,
		CategoryID:     mallItem.CategoryID,
		IPID:           mallItem.IPID,
		ItemSpecs:      mallItem.ItemSpecs,
		PurchasePrice:  itemInfo.PurchasePrice,
		SellListings:   itemInfo.SellListings,
		SalePrice:      mallItem.SalePrice,
		Discount:       mallItem.Discount,
		Freight:        mallItem.Freight,
		StockType:      mallItem.StockType,
		Stock:          mallItem.Stock,
		AvailableStock: mallItem.AvailableStock,
		TotalLimit:     mallItem.TotalLimit,
		DailyLimit:     mallItem.DailyLimit,
		StartTime:      mallItem.StartTime,
		Priority:       mallItem.Priority,
		CreatedAt:      mallItem.CreatedAt,
	}, nil
}

// AddMallItem 创建商品
func (s *Service) AddMallItem(req *define.AddMallItemReq) (*define.AddMallItemResp, error) {
	steamItem, err := logic.ValidateYcSteamItem(s.ctx, req.SkuID)
	if err != nil {
		return nil, err
	}
	itemInfo, err := logic.GetItemInfoByRemote(s.ctx, req.ItemID)
	if err != nil {
		return nil, define.MS200001Err
	}
	item := &model.MallItem{
		ID:             snowflakeutl.GenerateID(),
		ItemID:         req.ItemID,
		SkuID:          req.SkuID,
		SpuID:          steamItem.ProductNo,
		SalePrice:      req.SalePrice,
		Discount:       req.Discount,
		Freight:        *req.Freight,
		StockType:      req.StockType,
		Stock:          req.Stock,
		AvailableStock: req.Stock,
		TotalLimit:     *req.TotalLimit,
		DailyLimit:     *req.DailyLimit,
		StartTime:      req.StartTime,
		Priority:       req.Priority,
		CreatedBy:      s.GetAdminId(),
		UpdatedBy:      s.GetAdminId(),
	}
	if itemInfo != nil {
		item.ItemName = itemInfo.ItemName
		item.ItemSpecs = itemInfo.Specs
		item.ItemIconURL = itemInfo.IconUrl
		if len(itemInfo.TrademarkInfo) > 0 {
			item.TrademarkID = itemInfo.TrademarkInfo[len(itemInfo.TrademarkInfo)-1].ID
		}
		// 设置 CategoryID
		if err := logic.SetCategoryIDFromItemInfo(s.ctx, item, itemInfo); err != nil {
			return nil, err
		}

		// 设置 IPID
		if err := logic.SetIPIDFromItemInfo(s.ctx, item, itemInfo); err != nil {
			return nil, err
		}
	}
	schema := repo.GetQuery().MallItem
	if err := repo.NewMallItemRepo(schema.WithContext(s.ctx)).Save(item); err != nil {
		return nil, errors.Wrap(err, "保存商品失败")
	}
	return &define.AddMallItemResp{
		ID: item.ID,
	}, nil
}

// EditMallItem 更新商品信息
func (s *Service) EditMallItem(req *define.EditMallItemReq) (*define.EditMallItemResp, error) {
	mi := repo.GetQuery().MallItem
	wrapper := search.NewWrapper().Where(mi.ID.Eq(req.ID))
	mallItem, err := repo.NewMallItemRepo(mi.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		return nil, define.MS200001Err
	}

	var updates = map[string]interface{}{
		"sale_price":  req.SalePrice,
		"discount":    req.Discount,
		"freight":     req.Freight,
		"total_limit": req.TotalLimit,
		"daily_limit": req.DailyLimit,
		"priority":    req.Priority,
		"updated_by":  s.GetAdminId(),
	}

	if req.ItemID != mallItem.ItemID || req.SkuID != mallItem.SkuID {
		if mallItem.Status != enums.MallItemStatusWaiting.Val() {
			return nil, define.MS200008Err
		}

		steamItem, err := logic.ValidateYcSteamItem(s.ctx, req.SkuID)
		if err != nil {
			return nil, err
		}

		itemInfo, err := logic.GetItemInfoByRemote(s.ctx, req.ItemID)
		if err != nil {
			return nil, define.MS200001Err
		}

		if itemInfo != nil {
			if len(itemInfo.TrademarkInfo) > 0 {
				updates["spu_id"] = itemInfo.TrademarkInfo[len(itemInfo.TrademarkInfo)-1].ID
			}
			if len(itemInfo.ItemClassifyInfo) > 0 {
				updates["category_id"] = itemInfo.ItemClassifyInfo[len(itemInfo.ItemClassifyInfo)-1].ID
			}
			if len(itemInfo.IpInfo) > 0 {
				updates["ip_id"] = itemInfo.IpInfo[len(itemInfo.IpInfo)-1].ID
			}
			updates["item_id"] = req.ItemID
			updates["sku_id"] = req.SkuID
			updates["spu_id"] = steamItem.ProductNo
			updates["item_specs"] = itemInfo.Specs
			updates["item_icon_url"] = itemInfo.IconUrl
			updates["item_name"] = itemInfo.ItemName
		}
	}

	if mallItem.Status == enums.MallItemStatusWaiting.Val() || mallItem.Status == enums.MallItemStatusDown.Val() {
		updates["start_time"] = req.StartTime
		updates["stock"] = req.Stock
		updates["available_stock"] = req.Stock
	}

	err = repo.NewMallItemRepo(mi.WithContext(s.ctx)).UpdateField(updates, wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "更新商品失败")
	}

	if mallItem.Status == enums.MallItemStatusUp.Val() && mallItem.Priority != req.Priority {
		mallItem.Priority = req.Priority
		if err := logic.HandleIndexOnStatusChange(s.ctx, mallItem); err != nil {
			log.Ctx(s.ctx).Errorf("Failed to add cache: %v", err)
		}
	}

	return &define.EditMallItemResp{ID: mallItem.ID}, nil
}

// EditMallItemPriority 更新商品优先级
func (s *Service) EditMallItemPriority(req *define.EditMallItemPriorityReq) (*define.EditMallItemPriorityResp, error) {
	mi := repo.GetQuery().MallItem
	wrapper := search.NewWrapper().Where(mi.ID.Eq(req.ID))
	mallItem, err := repo.NewMallItemRepo(mi.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "查询直购商品失败")
	}
	updateItem := &model.MallItem{
		ID:        req.ID,
		Priority:  req.Priority,
		UpdatedBy: s.GetAdminId(),
	}
	if err := repo.NewMallItemRepo(mi.WithContext(s.ctx).Select(mi.Priority, mi.UpdatedBy)).UpdateById(updateItem); err != nil {
		return nil, errors.Wrap(err, "更新商品优先级失败")
	}
	if mallItem.Status == enums.MallItemStatusUp.Val() {
		mallItem.Priority = req.Priority
		if err := logic.HandleIndexOnStatusChange(s.ctx, mallItem); err != nil {
			log.Ctx(s.ctx).Errorf("Failed to add cache: %v", err)
		}
	}
	return &define.EditMallItemPriorityResp{ID: mallItem.ID}, nil
}

// EditMallItemStatus 更新商品信息状态
func (s *Service) EditMallItemStatus(req *define.EditMallItemStatusReq) (*define.EditMallItemStatusResp, error) {
	mi := repo.GetQuery().MallItem
	wrapper := search.NewWrapper().Where(mi.ID.Eq(req.ID))
	mallItem, err := repo.NewMallItemRepo(mi.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "查询直购商品失败")
	}
	if err := logic.ValidateStatusTransition(mallItem.Status, req.Status); err != nil {
		return nil, err
	}
	updateItem := &model.MallItem{
		ID:        req.ID,
		Status:    req.Status,
		UpdatedBy: s.GetAdminId(),
	}
	if req.Status == enums.MallItemStatusUp.Val() {
		// 查询是否已经存在上架的直购商品
		wrapper := search.NewWrapper().Where(mi.SkuID.Eq(mallItem.SkuID), mi.Status.Eq(enums.MallItemStatusUp.Val()))
		count, err := repo.NewMallItemRepo(mi.WithContext(s.ctx)).Count(wrapper)
		if err != nil {
			return nil, errors.Wrap(err, "查询直购商品失败")
		}
		if count > 0 {
			return nil, define.MS200009Err
		}
		steamItem, err := logic.ValidateYcSteamItem(s.ctx, mallItem.SkuID)
		if err != nil {
			return nil, err
		}
		if mallItem.Stock > steamItem.SellListings {
			return nil, define.MS200007Err
		}
		if mallItem.TotalLimit != 0 && mallItem.DailyLimit > mallItem.TotalLimit {
			return nil, define.MS200010Err
		}
		now := time.Now()
		updateItem.ShelfTime = &now
		mallItem.ShelfTime = &now

		// 更新商品信息为最新
		itemInfo, err := logic.GetItemInfoByRemote(s.ctx, mallItem.ItemID)
		if err != nil {
			return nil, define.MS200001Err
		}
		if itemInfo != nil {
			updateItem.ItemName = itemInfo.ItemName
			updateItem.ItemSpecs = itemInfo.Specs
			updateItem.ItemIconURL = itemInfo.IconUrl
			if len(itemInfo.TrademarkInfo) > 0 {
				updateItem.TrademarkID = itemInfo.TrademarkInfo[len(itemInfo.TrademarkInfo)-1].ID
			}
			// 设置 CategoryID
			if err := logic.SetCategoryIDFromItemInfo(s.ctx, updateItem, itemInfo); err != nil {
				return nil, err
			}

			// 设置 IPID
			if err := logic.SetIPIDFromItemInfo(s.ctx, updateItem, itemInfo); err != nil {
				return nil, err
			}
		}
	}
	if err := repo.NewMallItemRepo(mi.WithContext(s.ctx)).UpdateById(updateItem); err != nil {
		return nil, errors.Wrap(err, "更新商品失败")
	}
	if req.Status == enums.MallItemStatusDown.Val() {
		var orderIds []int64
		to := repo.GetQuery().TradeOrder
		toi := repo.GetQuery().TradeOrderItem
		err := to.WithContext(s.ctx).Select(to.ID).
			Where(to.OrderStatus.Eq(enums.OrderStatusUnPaid.Val())).
			Join(toi, toi.OrderID.EqCol(to.ID), toi.MallItemID.Eq(mallItem.ID)).
			Pluck(to.ID, &orderIds)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		now := time.Now()
		updateTradeOrder := &model.TradeOrder{
			OrderStatus: enums.OrderStatusCanceled.Val(),
			CancelType:  enums.CancelTypeWC.Val(),
			CancelAt:    &now,
		}
		_, err = to.WithContext(s.ctx).
			Where(to.ID.In(orderIds...), to.OrderStatus.Eq(enums.OrderStatusUnPaid.Val())).
			Updates(updateTradeOrder)
		if err != nil {
			log.Ctx(s.ctx).Errorf("下架商品取消所有待支付订单失败: %v", err)
			return nil, errors.Wrap(err, "取消所有待支付订单失败")
		}
	}
	if req.Status == enums.MallItemStatusUp.Val() || req.Status == enums.MallItemStatusDown.Val() {
		mallItem.Status = req.Status
		if err := logic.HandleIndexOnStatusChange(s.ctx, mallItem); err != nil {
			log.Ctx(s.ctx).Errorf("添加商品缓存失败: %v", err)
		}
		if err := logic.HandleStockCacheOnStatusChange(s.ctx, mallItem); err != nil {
			log.Ctx(s.ctx).Errorf("更新商品库存缓存失败: %v", err)
		}
	}
	return &define.EditMallItemStatusResp{ID: mallItem.ID}, nil
}

// WebGetMallItemList 用户端直购商品列表
func (s *Service) WebGetMallItemList(req *define.WebGetMallItemListReq) (*define.WebGetMallItemListResp, error) {
	resp := &define.WebGetMallItemListResp{
		List:    make([]*define.WebMallItemListData, 0),
		HasMore: false,
	}
	offset, pageSize := search.Paginate(req.GetPageSize(), req.GetPage())
	var mallItems []*model.MallItem
	var err error
	if req.IPID == "" {
		cacheKey := constant.GetMallItemListKey()
		opt := &redis.ZRangeBy{
			Min:    "-inf",
			Max:    "+inf",
			Offset: int64(offset),
			Count:  int64(pageSize),
		}
		mallItemIds, err := global.REDIS.ZRevRangeByScore(s.ctx, cacheKey, opt).Result()
		if err != nil {
			return nil, errors.Wrap(err, "获取商品列表失败")
		}
		miIDs := make([]int64, 0, len(mallItemIds))
		for _, idStr := range mallItemIds {
			if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
				miIDs = append(miIDs, id)
			}
		}
		if len(miIDs) == 0 {
			return resp, nil
		}
		mi := repo.GetQuery().MallItem
		qw := search.NewWrapper().Where(mi.ID.In(miIDs...)).OrderBy(mi.Priority.Desc(), mi.ShelfTime.Desc())
		mallItems, err = repo.NewMallItemRepo(mi.WithContext(s.ctx)).SelectList(qw)
		if err != nil {
			log.Ctx(s.ctx).Errorf("GetMarketChangesWebList SelectList err: %v, req: %+v", err, req)
			return nil, err
		}
	} else {
		mi := repo.GetQuery().MallItem
		wrapper := search.NewWrapper().Where(mi.StartTime.Lte(time.Now()), mi.IPID.Eq(req.IPID), mi.Status.Eq(enums.MallItemStatusUp.Val()))
		// 综合
		if req.OrderBy == 0 || req.OrderBy == enums.MallItemOrderByTypeDefault.Val() {
			wrapper = wrapper.OrderBy(mi.Priority.Desc(), mi.ShelfTime.Desc())
		}
		// 销量
		if req.OrderBy == enums.MallItemOrderByTypeSaleVolume.Val() {
			wrapper = wrapper.OrderBy(mi.SalesVolume.Desc(), mi.Priority.Desc(), mi.ShelfTime.Desc())
		}
		// 价格
		if req.OrderBy == enums.MallItemOrderByTypePrice.Val() {
			if req.SortOrder == constant.SortOrderDesc {
				wrapper = wrapper.OrderBy(mi.SalePrice.Desc(), mi.Priority.Desc(), mi.ShelfTime.Desc())
			}
			wrapper = wrapper.OrderBy(mi.SalePrice.Asc(), mi.Priority.Desc(), mi.ShelfTime.Desc())
		}
		mallItems, err = repo.NewMallItemRepo(mi.WithContext(s.ctx).Offset(offset).Limit(pageSize)).SelectList(wrapper)
		if err != nil {
			return nil, errors.Wrap(err, "获取商品列表失败")
		}
		if len(mallItems) == 0 {
			return resp, nil
		}
	}
	itemIds := make([]string, len(mallItems))
	for i, item := range mallItems {
		itemIds[i] = item.ItemID
	}
	is := repo.GetQuery().ItemStatistic
	isWrapper := search.NewWrapper().Select(is.ItemID, is.WishlistCount).Where(is.ItemID.In(itemIds...))
	itemStatistics, err := repo.NewItemStatisticRepo(is.WithContext(s.ctx)).SelectList(isWrapper)
	if err != nil {
		return nil, errors.Wrap(err, "获取商品统计信息失败")
	}
	itemStatisticsMap := make(map[string]*model.ItemStatistic, len(itemStatistics))
	for _, itemStatistic := range itemStatistics {
		itemStatisticsMap[itemStatistic.ItemID] = itemStatistic
	}
	mallItemListData := make([]*define.WebMallItemListData, 0, len(mallItems))
	for _, mallItem := range mallItems {
		mil := &define.WebMallItemListData{
			ID:            mallItem.ID,
			ItemID:        mallItem.ItemID,
			ItemName:      mallItem.ItemName,
			ItemIconURL:   mallItem.ItemIconURL,
			Priority:      mallItem.Priority,
			SalePrice:     mallItem.SalePrice,
			Discount:      mallItem.Discount,
			DiscountPrice: logic.CalculateDiscountedPrice(mallItem.SalePrice, mallItem.Discount),
			Sales:         mallItem.SalesVolume,
		}
		if itemStatistic, ok := itemStatisticsMap[mallItem.ItemID]; ok {
			mil.WishlistCount = itemStatistic.WishlistCount
		}
		mallItemListData = append(mallItemListData, mil)
	}
	resp.List = mallItemListData
	resp.HasMore = len(resp.List) >= req.GetPageSize()
	return resp, nil
}

// WebGetMallItemDetail 用户端直购商品详情
func (s *Service) WebGetMallItemDetail(req *define.WebGetMallItemDetailReq) (*define.WebGetMallItemDetailResp, error) {
	qw := search.NewWrapper()
	if req.ID > 0 {
		qw.Where(repo.GetQuery().MallItem.ID.Eq(req.ID))
	} else {
		qw.Where(repo.GetQuery().MallItem.ItemID.Eq(req.ItemID))
	}
	mi := repo.GetQuery().MallItem
	mallItem, err := repo.NewMallItemRepo(mi.WithContext(s.ctx)).SelectOne(qw)
	if err != nil {
		return nil, errors.Wrap(err, "查询商品详情失败")
	}
	resp := &define.WebGetMallItemDetailResp{
		ID:                 mallItem.ID,
		Status:             mallItem.Status,
		ItemID:             mallItem.ItemID,
		ItemName:           mallItem.ItemName,
		ItemIconUrl:        mallItem.ItemIconURL,
		TrademarkID:        mallItem.TrademarkID,
		CategoryID:         mallItem.CategoryID,
		IPID:               mallItem.IPID,
		Specs:              mallItem.ItemSpecs,
		Stock:              mallItem.Stock,
		AvailableStock:     mallItem.AvailableStock,
		SalePrice:          mallItem.SalePrice,
		Discount:           mallItem.Discount,
		DiscountPrice:      logic.CalculateDiscountedPrice(mallItem.SalePrice, mallItem.Discount),
		Freight:            mallItem.Freight,
		UserWishlistStatus: 0,
	}
	if itemInfo, _ := logic.GetItemInfo(s.ctx, mallItem.ItemID); itemInfo != nil {
		resp.ImageInfos = itemInfo.ImageInfos
		resp.Detail = itemInfo.DetailH5
	}
	if ipInfo, _ := logic.GetIpInfo(s.ctx, mallItem.IPID); ipInfo != nil {
		resp.IPName = ipInfo.Name
	}
	if categoryInfo, _ := logic.GetItemClassifyInfo(s.ctx, mallItem.CategoryID); categoryInfo != nil {
		resp.CategoryName = categoryInfo.Name
	}
	if trademarkInfo, _ := logic.GetTrademarkInfo(s.ctx, mallItem.TrademarkID); trademarkInfo != nil {
		resp.TrademarkName = trademarkInfo.Name
	}
	if s.GetUserId() != "" {
		userWishlist, _ := logic.IsWished(s.ctx, s.GetUserId(), mallItem.ItemID)
		if userWishlist != nil {
			resp.UserWishlistStatus = 1
		}
	}
	return resp, nil
}

// WebMallItemBuyBuy 直购下单购买
func (s *Service) WebMallItemBuyBuy(req *define.WebBuyReq) (*define.WebBuyResp, error) {
	// 初始化锁 用户不能同时下单
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewMallItemActionLock(locker.UserBuy, s.GetUserId())))
	if !l.Lock(s.ctx) {
		return nil, response.TooManyRequestErr
	}
	defer l.UnLock(s.ctx)
	mallItem, err := logic.ValidateMallItemForPurchase(s.ctx, req.MallItemID)
	if err != nil {
		return nil, errors.Wrap(err, "查询直购商品失败")
	}
	userId := s.GetUserId()
	if userId == "" {
		return nil, response.PermissionErr
	}
	// 校验用户购买数量
	if mallItem.DailyLimit > 0 && req.Quantity > mallItem.DailyLimit {
		return nil, define.MS200014Err.SetMsg(fmt.Sprintf(define.MS200014Err.Msg, mallItem.DailyLimit))
	}
	// 验证支付金额
	calculateOrderPrice := logic.CalculateOrderPrice(req.Quantity, mallItem)
	if calculateOrderPrice.PayAmount != req.PayAmount {
		return nil, define.MS200003Err
	}
	// 判断是否存在待支付订单
	pendingOrder, err := logic.CheckUserPendingOrder(s.ctx, userId, req.MallItemID)
	if err != nil {
		return nil, errors.Wrap(err, "查询用户待支付订单失败")
	}
	if pendingOrder != nil {
		return &define.WebBuyResp{
			OrderID:    pendingOrder.ID,
			PayAmount:  pendingOrder.PayAmount,
			CreatedAt:  pendingOrder.CreatedAt,
			IsNewOrder: false,
		}, nil
	}
	// 校验用户总限购
	if mallItem.TotalLimit > 0 {
		if err := logic.CheckUserTotalLimit(s.ctx, req, mallItem, userId); err != nil {
			return nil, err
		}
	}

	// 返回当前时间到当天结束的剩余秒数
	keyExpireSec, _ := utils.GetSecondsUntilEndOfDay()
	keys := []string{constant.GetMallItemStockKey(mallItem.ID), constant.GetUserBuyCountKey(mallItem.ID, s.GetUserId())}
	args := []interface{}{mallItem.DailyLimit, req.Quantity, keyExpireSec}
	result, err := global.REDIS.EvalSha(s.ctx, constant.MallItemDecrStockLuaSha, keys, args...).Int()
	if err != nil {
		log.Ctx(s.ctx).Errorf("Lua 脚本执行失败 key:%v, err:%+v", keys, err)
		return nil, global.CommonErr
	}
	if result == 0 {
		return nil, define.MS200004Err
	}
	if result == -1 {
		return nil, define.MS200014Err.SetMsg(fmt.Sprintf(define.MS200014Err.Msg, mallItem.DailyLimit))
	}
	tradeOrder, err := logic.BuildOrder(s.ctx, req, mallItem, calculateOrderPrice)
	if err != nil {
		return nil, errors.Wrap(err, "创建订单失败")
	}
	err = repo.ExecGenTx(s.ctx, func(tx context.Context) error {
		to := repo.Query(tx).TradeOrder
		if err := repo.NewTradeOrderRepo(to.WithContext(tx)).Save(tradeOrder); err != nil {
			return errors.Wrap(err, "创建订单失败")
		}
		// 扣减库存
		if err = logic.DecrMallItemStock(tx, req.MallItemID, req.Quantity); err != nil {
			return errors.Wrap(err, "扣减库存失败")
		}
		return nil
	})
	if err != nil {
		log.Ctx(s.ctx).Errorf("创建订单失败: %+v", err)
		return nil, global.CommonErr
	}

	return &define.WebBuyResp{
		OrderID:    tradeOrder.ID,
		PayAmount:  tradeOrder.PayAmount,
		CreatedAt:  time.Now(),
		IsNewOrder: true,
	}, nil
}

// OpenTriggerDisplayByStartTime 获取待显示的直购商品进行显示
func (s *Service) OpenTriggerDisplayByStartTime(req *define.OpenTriggerDisplayByStartTimeReq) (any, error) {
	spanContext := s.NewContextWithSpanContext(s.ctx)
	mi := repo.GetQuery().MallItem
	wrapper := search.NewWrapper().
		Where(mi.Status.Eq(enums.MallItemStatusUp.Val())).
		Where(mi.StartTime.Gte(time.Now().Add(-10 * time.Minute)))
	mallItems, err := repo.NewMallItemRepo(mi.WithContext(spanContext)).SelectList(wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "查询商品列表失败")
	}
	const batchSize = 50 // 控制每次处理数量，防止内存压力过大
	for i := 0; i < len(mallItems); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(mallItems) {
			endIndex = len(mallItems)
		}
		batch := mallItems[i:endIndex]
		if err := logic.ProcessBatchStartedToAddCache(spanContext, batch); err != nil {
			return nil, errors.Wrap(err, "处理待显示的直购商品列表失败")
		}
	}
	return response.Empty{}, nil
}

// OpenUpdateMallItemNotify 更新商品通知
func (s *Service) OpenUpdateMallItemNotify(req *define.OpenUpdateMallItemNotifyReq) (any, error) {
	spanContext := s.NewContextWithSpanContext(s.ctx)
	if len(req.ItemIds) == 0 {
		return response.Empty{}, nil
	}
	for _, itemId := range req.ItemIds {
		itemInfo, err := logic.GetItemInfoByRemote(spanContext, itemId)
		if err != nil {
			log.Ctx(spanContext).Errorf("查询商品失败: %+v", err)
			continue
		}
		if itemInfo == nil {
			log.Ctx(spanContext).Errorf("商品不存在: %+v", itemId)
			continue
		}
		// 查询直购商品数量
		mi := repo.Query(spanContext).MallItem
		wrapper := search.NewWrapper().Where(mi.ItemID.Eq(itemId), mi.Status.Eq(enums.MallItemStatusUp.Val()))
		count, err := repo.NewMallItemRepo(mi.WithContext(spanContext)).Count(wrapper)
		if err != nil {
			log.Ctx(spanContext).Errorf("查询商品数量失败: %+v", err)
			continue
		}
		if count > 0 {
			// 更新直购商品数据
			updateMallItem := &model.MallItem{
				ItemName:    itemInfo.ItemName,
				ItemSpecs:   itemInfo.Specs,
				ItemIconURL: itemInfo.IconUrl,
			}
			if len(itemInfo.TrademarkInfo) > 0 {
				updateMallItem.TrademarkID = itemInfo.TrademarkInfo[len(itemInfo.TrademarkInfo)-1].ID
			}
			// 设置 CategoryID
			if err := logic.SetCategoryIDFromItemInfo(s.ctx, updateMallItem, itemInfo); err != nil {
				return nil, err
			}

			// 设置 IPID
			if err := logic.SetIPIDFromItemInfo(s.ctx, updateMallItem, itemInfo); err != nil {
				return nil, err
			}
			if err := repo.NewMallItemRepo(mi.WithContext(spanContext)).Update(updateMallItem, wrapper); err != nil {
				log.Ctx(spanContext).Errorf("更新商品失败: %+v", err)
				continue
			}
		}
	}
	return response.Empty{}, nil
}
