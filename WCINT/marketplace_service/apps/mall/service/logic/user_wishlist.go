package logic

import (
	"context"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/pkg/search"
)

func IsWished(ctx context.Context, userID, itemID string) (*model.UserWishlist, error) {
	uw := repo.GetQuery().UserWishlist
	uwWrapper := search.NewWrapper().Where(uw.ItemID.Eq(itemID), uw.UserID.Eq(userID), uw.Status.Eq(enums.UserWishlistStatusWant.Val()))
	userWishlist, err := repo.NewUserWishlistRepo(uw.WithContext(ctx).Limit(1)).SelectOne(uwWrapper)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return userWishlist, nil
}
