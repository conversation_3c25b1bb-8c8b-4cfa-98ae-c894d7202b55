package logic

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/global"
	"marketplace_service/pkg/cache"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/pat"
)

// GetUserDetail 获取用户详情
func GetUserDetail(ctx context.Context, id string) (*pat.UserDetail, error) {
	var userDetail *pat.UserDetail

	cacheKey := constant.GetUserDetailKey(id)
	err := cache.GetFromCache(ctx, cacheKey, &userDetail)
	if errors.Is(err, redis.Nil) {
		userDetail, err = GetUserDetailByRemote(ctx, id)
		if err != nil {
			log.Ctx(ctx).Errorf("redis获取用户信息失败 err:%+v", err)
			return nil, err
		}
	} else if err != nil {
		log.Ctx(ctx).Errorf("redis获取用户信息失败 err:%+v", err)
		return nil, err
	}
	return userDetail, nil
}

// GetUserDetailByRemote 获取用户详情(不使用缓存,强制刷新缓存)
func GetUserDetailByRemote(ctx context.Context, id string) (*pat.UserDetail, error) {
	var userDetail *pat.UserDetail

	userDetails, err := pat.GetUserDetails(ctx, []string{id})
	if err != nil {
		log.Ctx(ctx).Errorf("查询用户错误 err:%+v", err)
		return nil, response.ParamErr.SetMsg("查询用户错误")
	}
	if len(userDetails) > 0 {
		userDetail = userDetails[0]
		cacheKey := constant.GetUserDetailKey(id)
		if err := cache.SetToCache(ctx, cacheKey, userDetail, constant.UserDetailTTL); err != nil {
			log.Ctx(ctx).Errorf("redis设置用户详情缓存失败 err:%+v", err)
		}
		log.Ctx(ctx).Infof("redis设置用户详情缓存成功 userId:%v", id)
	}
	return userDetail, err
}

// GetUserDetails 获取用户详情列表
func GetUserDetails(ctx context.Context, ids ...string) ([]*pat.UserDetail, error) {
	result := make([]*pat.UserDetail, 0)
	var queryUserIds []string
	var userIdRedisKey []string
	for _, id := range ids {
		userIdRedisKey = append(userIdRedisKey, constant.GetUserDetailKey(id))
	}
	userDetailStrList, _ := global.REDIS.MGet(ctx, userIdRedisKey...).Result()

	if userDetailStrList != nil && len(userDetailStrList) > 0 {
		for k, userStr := range userDetailStrList {
			if userStr != nil && userStr != "" {
				userDetail := &pat.UserDetail{}
				err := utils.MsgpackUnmarshal([]byte(userStr.(string)), userDetail)
				if err != nil {
					log.Ctx(ctx).Errorf("redis用户详情转换结构体失败 err:%+v", err)
				} else {
					result = append(result, userDetail)
				}
			} else {
				queryUserIds = append(queryUserIds, ids[k])
			}
		}
	}

	if len(queryUserIds) > 0 {
		userDetails, err := pat.GetUserDetails(ctx, queryUserIds)
		if err != nil {
			log.Ctx(ctx).Errorf("查询用户列表错误 err:%+v", err)
			return nil, response.ParamErr.SetMsg("查询用户列表错误")
		}

		for _, userDetail := range userDetails {
			result = append(result, userDetail)
			cacheKey := constant.GetUserDetailKey(userDetail.Id)
			if err := cache.SetToCache(ctx, cacheKey, userDetail, constant.UserDetailTTL); err != nil {
				log.Ctx(ctx).Errorf("redis设置用户详情缓存失败 err:%+v", err)
			}
			log.Ctx(ctx).Infof("redis设置用户详情缓存成功 userId:%v", userDetail.Id)
		}
	}
	return result, nil
}

// GetUserDetailMap 获取用户详情切片
func GetUserDetailMap(ctx context.Context, ids ...string) (map[string]*pat.UserDetail, error) {
	userDetails, err := GetUserDetails(ctx, ids...)
	if err != nil {
		return nil, err
	}
	userDetailMap := make(map[string]*pat.UserDetail, len(userDetails))
	for _, userDetail := range userDetails {
		userDetailMap[userDetail.Id] = userDetail
	}
	return userDetailMap, nil
}

// GetUserDetailsByRemote 获取用户详情列表(不使用缓存,强制刷新缓存)
func GetUserDetailsByRemote(ctx context.Context, ids ...string) ([]*pat.UserDetail, error) {
	result := make([]*pat.UserDetail, 0)
	userDetails, err := pat.GetUserDetails(ctx, ids)
	if err != nil {
		log.Ctx(ctx).Errorf("查询remote用户列表错误 err:%+v", err)
		return nil, response.ParamErr.SetMsg("查询remote用户列表错误")
	}

	for _, userDetail := range userDetails {
		result = append(result, userDetail)
		cacheKey := constant.GetUserDetailKey(userDetail.Id)
		if err := cache.SetToCache(ctx, cacheKey, userDetail, constant.UserDetailTTL); err != nil {
			log.Ctx(ctx).Errorf("redis设置用户详情缓存失败 err:%+v", err)
		}
		log.Ctx(ctx).Infof("redis设置用户详情缓存成功 userId:%v", userDetail.Id)
	}
	return result, nil
}

// GetUserDetailsByRemoteMap 获取用户详情切片(不使用缓存,强制刷新缓存)
func GetUserDetailsByRemoteMap(ctx context.Context, ids ...string) (map[string]*pat.UserDetail, error) {
	userDetails, err := GetUserDetailsByRemote(ctx, ids...)
	if err != nil {
		return nil, err
	}
	userDetailMap := make(map[string]*pat.UserDetail, len(userDetails))
	for _, userDetail := range userDetails {
		userDetailMap[userDetail.Id] = userDetail
	}
	return userDetailMap, nil
}
