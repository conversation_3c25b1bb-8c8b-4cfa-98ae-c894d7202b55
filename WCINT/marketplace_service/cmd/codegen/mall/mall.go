package main

import (
	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func genModelFrm() {
	dsn := "root:nnA6MXpPQhqkxxEBLo@tcp(127.0.0.1:8002)/marketplace_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/mall/dal/query",
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true,
	})
	g.UseDB(db)

	isDelType := gen.FieldType("is_del", "soft_delete.DeletedAt")
	isDelFlag := gen.FieldGORMTag("is_del", func(tag field.GormTag) field.GormTag {
		return tag.Append("softDelete", "flag")
	})
	g.WithOpts(isDelType, isDelFlag)

	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"tinyint": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*int32"
			}
			return "int32"
		},
		"int": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*int32"
			}
			return "int32"
		},
		"bigint": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*int64"
			}
			return "int64"
		},
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
		"json": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*datatypes.JSON"
			}
			return "datatypes.JSON"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	mallItem := g.GenerateModelAs("mall_item", "MallItem")

	tradeOrderItem := g.GenerateModelAs("trade_order_item", "TradeOrderItem")

	tradeOrder := g.GenerateModelAs("trade_order", "TradeOrder",
		gen.FieldRelate(field.HasMany, "TradeOrderItem", tradeOrderItem,
			&field.RelateConfig{
				RelateSlicePointer: true,
				GORMTag:            (field.GormTag{}).Append("foreignKey", "order_id"),
			},
		),
	)

	itemStatistic := g.GenerateModelAs("item_statistic", "ItemStatistic")

	userWishlist := g.GenerateModelAs("user_wishlist", "UserWishlist")

	tradeOrderFreight := g.GenerateModelAs("trade_order_freight", "TradeOrderFreight")

	applyBasic := make([]interface{}, 0)
	applyBasic = append(
		applyBasic, mallItem, tradeOrder, tradeOrderItem, userWishlist, itemStatistic, tradeOrderFreight,
	)
	g.ApplyBasic(applyBasic...)
	g.ApplyInterface(func(method model.Method) {}, applyBasic...)
	g.Execute()
}

func genRepoFrm() {
	GenRepo("mall", "MallItem", "mall_item", "IMallItemDo", "MallItem")
	GenRepo("mall", "TradeOrderItem", "trade_order_item", "ITradeOrderItemDo", "TradeOrderItem")
	GenRepo("mall", "TradeOrder", "trade_order", "ITradeOrderDo", "TradeOrder")
	GenRepo("mall", "UserWishlist", "user_wishlist", "IUserWishlistDo", "UserWishlist")
	GenRepo("mall", "ItemStatistic", "item_statistic", "IItemStatisticDo", "ItemStatistic")
	GenRepo("mall", "TradeOrderFreight", "trade_order_freight", "ITradeOrderFreightDo", "TradeOrderFreight")
}

func main() {
	genModelFrm()
	genRepoFrm()
}
