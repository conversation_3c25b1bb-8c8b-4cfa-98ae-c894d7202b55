package main

import (
	"context"
	"fmt"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/propagation"
	"marketplace_service/apps/mall/constant"
	"os"
	"strings"
	"time"

	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	"e.coding.net/g-dtay0385/common/go-initialize"
	"e.coding.net/g-dtay0385/common/go-kafka/manager"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-redis"
	"github.com/go-micro/plugins/v4/server/http"
	"github.com/go-micro/plugins/v4/wrapper/trace/opentelemetry"
	"github.com/urfave/cli/v2"
	"go-micro.dev/v4"
	"go-micro.dev/v4/server"
	"marketplace_service/apps"
	_ "marketplace_service/docs"
	"marketplace_service/global"
	"marketplace_service/pkg/utils/snowflakeutl"

	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
)

//go:generate swag init -d ../apps  -g ../cmd/main.go -o ../docs --instanceName marketplace_service --parseDependency --parseDepth 6  --parseInternal

// @title app API
// @version 1.0.0
// @description 接口文档

// @securityDefinitions.apikey Bearer
// @in header
// @name Authorization
func main() {
	// 获取服务实例
	s := GetService()

	//初始化时区
	location, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = location

	s.Init()
	err := s.Run()
	if err != nil {
		panic(fmt.Sprintf("服务启动失败 err:%+v", err))
	}
}

// GetService 获取服务
func GetService() micro.Service {
	// 初始化组件
	initComponents()

	// 初始化雪花id生成器
	snowflakeutl.Init(global.REDIS.Client)

	// 构建服务
	return buildService()
}

// 初始化组件
func initComponents() {
	// 加载配置
	options := make([]initialize.Option, 0)
	env := strings.ToLower(os.Getenv("ENV"))
	if env == global.EnvDev {
		options = append(options, initialize.WithConfigFile("./configs/config-dev.yaml"))
	}
	initializer := initialize.New(global.GlobalConfig, options...)

	// 初始化组件
	initializer.InitComponents(
		// 日志
		initialize.WithLoggerConfig(global.GlobalConfig.Logger),
		// mysql
		initialize.WithMysqlConfig(initialize.MysqlConfig{Mysql: global.GlobalConfig.Mysql}),
		// redis
		initialize.WithRedisConfig(initialize.RedisConfig{Redis: global.GlobalConfig.Redis}),
		// kafka
		initialize.WithKafkaBrokersConfig(global.GlobalConfig.Kafka),
	)
	//初始化Global
	initGlobal()

	// 加载redis脚本
	loadRedisScript()

	// 初始化 tracer
	initTracer()

}

// 构建http服务器
func buildHttpServer() server.Server {
	// 初始化路由
	r := apps.Init()
	// 加载路由
	for _, f := range global.Routers {
		f()
	}

	// 构建http服务器
	srv := http.NewServer(
		server.Address(global.GlobalConfig.Service.Address),
		server.Name(global.GlobalConfig.Service.Name),
		server.Version(global.GlobalConfig.Service.Version),
		server.WithLogger(log.Logger),
	)

	// 绑定路由
	hd := server.NewHandler(r)
	err := srv.Handle(hd)
	if err != nil {
		panic(fmt.Sprintf("路由绑定失败 err:%+v", err))
	}

	// 初始化主站请求
	request.InitNodeHttpConfigs(global.GlobalConfig.MasterHttp)

	return srv
}

// 构建服务
func buildService() micro.Service {
	return micro.NewService(
		micro.Name(global.GlobalConfig.Service.Name),
		micro.Server(buildHttpServer()),
		micro.WrapHandler(opentelemetry.NewHandlerWrapper()),
		micro.WrapCall(opentelemetry.NewCallWrapper()),
		micro.Flags(&cli.StringFlag{Name: "confFile"}),
	)
}

// 初始化 Global
func initGlobal() {
	//Redis
	global.REDIS = redis.GetClient("marketplace_service")

	//Kafka
	global.BROKER = manager.GetBroker(global.GlobalConfig.Kafka[0].Name)
}

func initTracer() {
	if global.GlobalConfig.Service.Env == global.EnvDev {
		return
	}
	logPrefix := "[initTracer]"
	exporter, err := stdouttrace.New(stdouttrace.WithPrettyPrint())
	if err != nil {
		log.Fatal(logPrefix+" new exporter error:", err)
		return
	}
	tp := sdktrace.NewTracerProvider(
		sdktrace.WithSampler(sdktrace.AlwaysSample()),
		sdktrace.WithBatcher(exporter),
	)
	otel.SetTracerProvider(tp)
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(propagation.TraceContext{}, propagation.Baggage{}))
}

func loadRedisScript() {
	constant.MallItemDecrStockLuaSha = global.PrepareScript(context.Background(), constant.MallItemDecrStockLua)
	constant.MallItemRollbackStockLuaSha = global.PrepareScript(context.Background(), constant.MallItemRollbackStockLua)
}
